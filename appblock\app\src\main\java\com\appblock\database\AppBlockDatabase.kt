package com.appblock.database

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import com.appblock.database.dao.AppUsageDao
import com.appblock.database.dao.BlockedAppDao
import com.appblock.database.entity.AppUsageEntity
import com.appblock.database.entity.BlockedAppEntity

@Database(
    entities = [BlockedAppEntity::class, AppUsageEntity::class],
    version = 1,
    exportSchema = false
)
abstract class AppBlockDatabase : RoomDatabase() {
    
    abstract fun blockedAppDao(): BlockedAppDao
    abstract fun appUsageDao(): AppUsageDao
    
    companion object {
        @Volatile
        private var INSTANCE: AppBlockDatabase? = null
        
        fun getDatabase(context: Context): AppBlockDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    AppBlockDatabase::class.java,
                    "appblock_database"
                ).build()
                INSTANCE = instance
                instance
            }
        }
    }
}
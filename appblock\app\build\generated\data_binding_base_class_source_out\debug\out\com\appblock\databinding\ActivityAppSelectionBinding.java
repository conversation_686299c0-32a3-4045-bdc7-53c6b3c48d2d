// Generated by view binder compiler. Do not edit!
package com.appblock.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.appblock.R;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityAppSelectionBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final MaterialButton btnShowAll;

  @NonNull
  public final MaterialButton btnShowSystemApps;

  @NonNull
  public final MaterialButton btnShowUserApps;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final RecyclerView recyclerView;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView tvAppCount;

  private ActivityAppSelectionBinding(@NonNull CoordinatorLayout rootView,
      @NonNull MaterialButton btnShowAll, @NonNull MaterialButton btnShowSystemApps,
      @NonNull MaterialButton btnShowUserApps, @NonNull ProgressBar progressBar,
      @NonNull RecyclerView recyclerView, @NonNull Toolbar toolbar, @NonNull TextView tvAppCount) {
    this.rootView = rootView;
    this.btnShowAll = btnShowAll;
    this.btnShowSystemApps = btnShowSystemApps;
    this.btnShowUserApps = btnShowUserApps;
    this.progressBar = progressBar;
    this.recyclerView = recyclerView;
    this.toolbar = toolbar;
    this.tvAppCount = tvAppCount;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityAppSelectionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityAppSelectionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_app_selection, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityAppSelectionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_show_all;
      MaterialButton btnShowAll = ViewBindings.findChildViewById(rootView, id);
      if (btnShowAll == null) {
        break missingId;
      }

      id = R.id.btn_show_system_apps;
      MaterialButton btnShowSystemApps = ViewBindings.findChildViewById(rootView, id);
      if (btnShowSystemApps == null) {
        break missingId;
      }

      id = R.id.btn_show_user_apps;
      MaterialButton btnShowUserApps = ViewBindings.findChildViewById(rootView, id);
      if (btnShowUserApps == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.recycler_view;
      RecyclerView recyclerView = ViewBindings.findChildViewById(rootView, id);
      if (recyclerView == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tv_app_count;
      TextView tvAppCount = ViewBindings.findChildViewById(rootView, id);
      if (tvAppCount == null) {
        break missingId;
      }

      return new ActivityAppSelectionBinding((CoordinatorLayout) rootView, btnShowAll,
          btnShowSystemApps, btnShowUserApps, progressBar, recyclerView, toolbar, tvAppCount);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

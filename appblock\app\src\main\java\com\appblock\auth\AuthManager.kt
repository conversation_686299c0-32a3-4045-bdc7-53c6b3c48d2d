package com.appblock.auth

import android.content.Context
import android.provider.Settings
import android.util.Log
import com.appblock.api.ApiClient
import com.appblock.model.RegistrationRequest
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class AuthManager private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "AuthManager"
        
        @Volatile
        private var INSTANCE: AuthManager? = null
        
        fun getInstance(context: Context): AuthManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: AuthManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val apiClient = ApiClient.getInstance(context)
    
    suspend fun registerDevice(registrationCode: String): Result<String> {
        return withContext(Dispatchers.IO) {
            try {
                val deviceId = Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID)
                val deviceName = android.os.Build.MODEL
                val deviceModel = "${android.os.Build.MANUFACTURER} ${android.os.Build.MODEL}"
                
                val request = RegistrationRequest(
                    deviceId = deviceId,
                    deviceName = deviceName,
                    deviceModel = deviceModel
                )
                
                val response = apiClient.useRegistrationCode(registrationCode, request)
                
                if (response.isSuccessful && response.body()?.success == true) {
                    val authResponse = response.body()?.data
                    if (authResponse != null) {
                        apiClient.saveTokens(authResponse.token, authResponse.refreshToken)
                        Log.d(TAG, "Device registered successfully")
                        Result.success("Device registered successfully")
                    } else {
                        Result.failure(Exception("Invalid response data"))
                    }
                } else {
                    val errorMessage = response.body()?.error ?: "Registration failed"
                    Log.e(TAG, "Registration failed: $errorMessage")
                    Result.failure(Exception(errorMessage))
                }
            } catch (e: Exception) {
                Log.e(TAG, "Registration error", e)
                Result.failure(e)
            }
        }
    }
    
    fun isAuthenticated(): Boolean {
        return apiClient.isAuthenticated()
    }
    
    fun logout() {
        apiClient.clearTokens()
        Log.d(TAG, "User logged out")
    }
    
    suspend fun getCurrentUser() = withContext(Dispatchers.IO) {
        try {
            val response = apiClient.getCurrentUser()
            if (response.isSuccessful && response.body()?.success == true) {
                response.body()?.data
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting current user", e)
            null
        }
    }
}
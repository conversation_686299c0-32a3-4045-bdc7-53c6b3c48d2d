<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Required permissions for app blocking functionality -->
    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <!-- VPN permissions for network-level website blocking -->
    <uses-permission android:name="android.permission.BIND_VPN_SERVICE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <application
        android:allowBackup="true"
        android:label="App Block"
        android:supportsRtl="true"
        android:theme="@android:style/Theme.Material.Light"
        tools:targetApi="31">

        <!-- Main Activity -->
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:label="App Block">
            <!-- Removed theme reference -->
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name=".ui.RegistrationActivity"
            android:exported="false"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar" />

        <!-- App Selection Activity -->
        <activity
            android:name=".ui.SimpleAppSelectionActivity"
            android:exported="false"
            android:label="Select Apps to Block" />

        <!-- Permission Setup Activity -->
        <activity
            android:name=".ui.PermissionSetupActivity"
            android:exported="false"
            android:label="App Block Setup" />

        <!-- Accessibility Service for app monitoring -->
        <service
            android:name=".service.AppBlockAccessibilityService"
            android:exported="false"
            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE">
            <intent-filter>
                <action android:name="android.accessibilityservice.AccessibilityService" />
            </intent-filter>
            <meta-data
                android:name="android.accessibilityservice"
                android:resource="@xml/accessibility_service_config" />
        </service>

        <!-- Usage Stats Monitoring Service -->
        <service
            android:name=".service.UsageStatsMonitorService"
            android:exported="false"
            android:process=":monitor"
            android:foregroundServiceType="specialUse" />

        <!-- VPN Service for network-level website blocking -->
        <service
            android:name=".service.WebBlockVpnService"
            android:exported="false"
            android:permission="android.permission.BIND_VPN_SERVICE">
            <intent-filter>
                <action android:name="android.net.VpnService" />
            </intent-filter>
        </service>

        <!-- Device Admin Receiver -->
        <receiver
            android:name=".receiver.AppBlockDeviceAdminReceiver"
            android:exported="true"
            android:permission="android.permission.BIND_DEVICE_ADMIN">
            <meta-data
                android:name="android.app.device_admin"
                android:resource="@xml/device_admin" />
            <intent-filter>
                <action android:name="android.app.action.DEVICE_ADMIN_ENABLED" />
                <action android:name="android.app.action.DEVICE_ADMIN_DISABLED" />
            </intent-filter>
        </receiver>

        <!-- Boot Receiver -->
        <receiver
            android:name=".receiver.BootReceiver"
            android:exported="true">
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
                <action android:name="android.intent.action.PACKAGE_REPLACED" />
                <data android:scheme="package" />
            </intent-filter>
        </receiver>

        <!-- Permission Monitor Receiver -->
        <receiver
            android:name=".receiver.PermissionMonitorReceiver"
            android:exported="false" />

    </application>

</manifest>

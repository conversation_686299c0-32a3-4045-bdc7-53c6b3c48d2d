package com.appblock.data

import android.content.Context
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.util.Log
import com.appblock.database.AppBlockDatabase
import com.appblock.database.entity.BlockedAppEntity
import com.appblock.service.WebBlockVpnService
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import java.text.SimpleDateFormat
import java.util.*

class BlockedAppsManager private constructor(private val context: Context) {

    companion object {
        private const val TAG = "BlockedAppsManager"
        
        @Volatile
        private var INSTANCE: BlockedAppsManager? = null
        
        fun getInstance(context: Context): BlockedAppsManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: BlockedAppsManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }

    private val database = AppBlockDatabase.getDatabase(context)
    private val blockedAppDao = database.blockedAppDao()
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    // For temporary allow functionality
    private val temporaryAllowCache = mutableMapOf<String, Long>()

    // Flow for observing blocked apps
    fun getBlockedAppsFlow(): Flow<List<BlockedAppEntity>> {
        return blockedAppDao.getAllBlockedApps()
    }

    // Check if an app is currently blocked
    suspend fun isAppBlocked(packageName: String): Boolean {
        return withContext(Dispatchers.IO) {
            // Check temporary allow first
            val allowedUntil = temporaryAllowCache[packageName]
            if (allowedUntil != null && System.currentTimeMillis() < allowedUntil) {
                return@withContext false
            } else if (allowedUntil != null) {
                // Remove expired temporary allow
                temporaryAllowCache.remove(packageName)
            }

            val blockedApp = blockedAppDao.isAppBlocked(packageName)
            if (blockedApp == null) return@withContext false

            // Check time-based restrictions
            if (blockedApp.startTime != null && blockedApp.endTime != null) {
                return@withContext isWithinTimeRange(blockedApp.startTime, blockedApp.endTime)
            }

            true
        }
    }

    // Add a blocked app
    suspend fun addBlockedApp(packageName: String, appName: String? = null, timeLimit: Int? = null, startTime: String? = null, endTime: String? = null) {
        withContext(Dispatchers.IO) {
            val resolvedAppName = appName ?: getAppName(packageName)
            val blockedApp = BlockedAppEntity(
                packageName = packageName,
                appName = resolvedAppName,
                isBlocked = true,
                timeLimit = timeLimit,
                startTime = startTime,
                endTime = endTime,
                syncedWithServer = false // Mark as needing sync
            )
            blockedAppDao.insertBlockedApp(blockedApp)
            Log.d(TAG, "Added blocked app: $packageName")
        }
    }

    // Remove a blocked app
    suspend fun removeBlockedApp(packageName: String) {
        withContext(Dispatchers.IO) {
            blockedAppDao.deleteBlockedAppByPackage(packageName)
            Log.d(TAG, "Removed blocked app: $packageName")
        }
    }

    // Update app from server data
    suspend fun updateAppFromServer(packageName: String, isBlocked: Boolean, timeLimit: Int? = null, startTime: String? = null, endTime: String? = null) {
        withContext(Dispatchers.IO) {
            val appName = getAppName(packageName)
            val blockedApp = BlockedAppEntity(
                packageName = packageName,
                appName = appName,
                isBlocked = isBlocked,
                timeLimit = timeLimit,
                startTime = startTime,
                endTime = endTime,
                syncedWithServer = true // This came from server
            )
            blockedAppDao.insertBlockedApp(blockedApp)
            Log.d(TAG, "Updated app from server: $packageName, blocked: $isBlocked")
        }
    }

    // Batch update from server
    suspend fun updateAppsFromServer(apps: List<BlockedAppEntity>) {
        withContext(Dispatchers.IO) {
            val updatedApps = apps.map { it.copy(syncedWithServer = true) }
            blockedAppDao.insertBlockedApps(updatedApps)
            Log.d(TAG, "Batch updated ${apps.size} apps from server")
        }
    }

    // Get all currently blocked app package names (for quick lookup)
    suspend fun getBlockedPackageNames(): Set<String> {
        return withContext(Dispatchers.IO) {
            val blockedApps = blockedAppDao.getAllBlockedApps().first()
            val currentTime = SimpleDateFormat("HH:mm", Locale.getDefault()).format(Date())
            
            blockedApps.filter { app ->
                if (app.startTime != null && app.endTime != null) {
                    isWithinTimeRange(app.startTime, app.endTime)
                } else {
                    app.isBlocked
                }
            }.map { it.packageName }.toSet()
        }
    }

    // Check if current time is within the blocked time range
    private fun isWithinTimeRange(startTime: String, endTime: String): Boolean {
        return try {
            val currentTime = SimpleDateFormat("HH:mm", Locale.getDefault()).format(Date())
            val current = SimpleDateFormat("HH:mm", Locale.getDefault()).parse(currentTime)?.time ?: 0
            val start = SimpleDateFormat("HH:mm", Locale.getDefault()).parse(startTime)?.time ?: 0
            val end = SimpleDateFormat("HH:mm", Locale.getDefault()).parse(endTime)?.time ?: 0

            if (start <= end) {
                // Same day range (e.g., 09:00 to 17:00)
                current in start..end
            } else {
                // Overnight range (e.g., 22:00 to 06:00)
                current >= start || current <= end
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing time range", e)
            false
        }
    }

    // Get app name from package name
    private fun getAppName(packageName: String): String {
        return try {
            val packageManager = context.packageManager
            val applicationInfo = packageManager.getApplicationInfo(packageName, 0)
            packageManager.getApplicationLabel(applicationInfo).toString()
        } catch (e: Exception) {
            packageName // Fallback to package name
        }
    }

    // Website blocking methods (keep existing functionality)
    fun isWebsiteBlocked(url: String): Boolean {
        val domain = extractDomain(url)
        return getBlockedWebsites().any { blockedDomain ->
            domain.contains(blockedDomain, ignoreCase = true) ||
            blockedDomain.contains(domain, ignoreCase = true)
        }
    }

    private fun extractDomain(url: String): String {
        return try {
            val cleanUrl = url.removePrefix("http://").removePrefix("https://").removePrefix("www.")
            cleanUrl.split("/")[0].lowercase()
        } catch (e: Exception) {
            url.lowercase()
        }
    }

    // Keep your existing website blocking methods
    fun getBlockedWebsites(): Set<String> {
        return setOf(
            "facebook.com", "instagram.com", "tiktok.com", "twitter.com", "x.com",
            "snapchat.com", "youtube.com", "netflix.com", "reddit.com", "discord.com",
            "twitch.tv", "spotify.com", "pornhub.com", "xvideos.com", "gambling.com",
            "en.betway.co.tz"
        )
    }

    // For VPN service compatibility
    fun isVpnBlockingEnabled(): Boolean {
        // Return true if VPN blocking should be used
        return WebBlockVpnService.isServiceRunning()
    }

    // For settings blocking compatibility
    fun isSettingsBlocked(): Boolean {
        // Return true if settings app should be blocked
        return false // Adjust based on your needs
    }



    fun temporarilyAllowApp(packageName: String, durationMs: Long) {
        val allowedUntil = System.currentTimeMillis() + durationMs
        temporaryAllowCache[packageName] = allowedUntil
        Log.d(TAG, "Temporarily allowed app $packageName for ${durationMs / 1000} seconds")
    }

    // Check if website is blocked (placeholder for future implementation)
    fun isWebsiteBlocked(url: String): Boolean {
        // TODO: Implement website blocking logic
        return false
    }

    // Cleanup resources
    fun cleanup() {
        scope.cancel()
    }

    // For VPN service compatibility
    fun isVpnBlockingEnabled(): Boolean {
        // Return true if VPN blocking should be used
        return true
    }

    // For settings blocking compatibility  
    fun isSettingsBlocked(): Boolean {
        // Return true if settings app should be blocked
        return false // Adjust based on your needs
    }

    // For temporary allow functionality
    private val temporaryAllowCache = mutableMapOf<String, Long>()

    fun temporarilyAllowApp(packageName: String, durationMs: Long) {
        val allowedUntil = System.currentTimeMillis() + durationMs
        temporaryAllowCache[packageName] = allowedUntil
        Log.d(TAG, "Temporarily allowed app $packageName for ${durationMs / 1000} seconds")
    }

    // Update the isAppBlocked method to check temporary allows
    suspend fun isAppBlocked(packageName: String): Boolean {
        return withContext(Dispatchers.IO) {
            // Check temporary allow first
            val allowedUntil = temporaryAllowCache[packageName]
            if (allowedUntil != null && System.currentTimeMillis() < allowedUntil) {
                return@withContext false
            } else if (allowedUntil != null) {
                // Remove expired temporary allow
                temporaryAllowCache.remove(packageName)
            }
            
            val blockedApp = blockedAppDao.isAppBlocked(packageName)
            if (blockedApp == null) return@withContext false
            
            // Check time-based restrictions
            if (blockedApp.startTime != null && blockedApp.endTime != null) {
                return@withContext isWithinTimeRange(blockedApp.startTime, blockedApp.endTime)
            }
            
            true
        }
    }
}
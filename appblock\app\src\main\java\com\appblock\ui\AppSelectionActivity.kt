package com.appblock.ui

import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.SearchView
import androidx.recyclerview.widget.LinearLayoutManager
import com.appblock.R
import com.appblock.data.BlockedAppsManager
import com.appblock.databinding.ActivityAppSelectionBinding
import com.appblock.ui.adapter.AppListAdapter
import kotlinx.coroutines.*
import kotlinx.coroutines.runBlocking

class AppSelectionActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "AppSelectionActivity"
    }

    private lateinit var binding: ActivityAppSelectionBinding
    private lateinit var blockedAppsManager: BlockedAppsManager
    private lateinit var appListAdapter: AppListAdapter
    
    private var allApps = listOf<AppInfo>()
    private var filteredApps = listOf<AppInfo>()
    private val coroutineScope = CoroutineScope(Dispatchers.Main + SupervisorJob())

    data class AppInfo(
        val packageName: String,
        val appName: String,
        val icon: android.graphics.drawable.Drawable,
        val isSystemApp: Boolean,
        var isBlocked: Boolean = false
    )

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityAppSelectionBinding.inflate(layoutInflater)
        setContentView(binding.root)

        blockedAppsManager = BlockedAppsManager.getInstance(this)
        
        setupUI()
        loadApps()
    }

    private fun setupUI() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = "Select Apps to Block"

        // Setup RecyclerView
        appListAdapter = AppListAdapter { appInfo, isChecked ->
            handleAppSelectionChanged(appInfo, isChecked)
        }
        
        binding.recyclerView.apply {
            layoutManager = LinearLayoutManager(this@AppSelectionActivity)
            adapter = appListAdapter
        }

        // Setup filter buttons
        binding.btnShowAll.setOnClickListener {
            filterApps(showAll = true, showSystemApps = false)
        }
        
        binding.btnShowUserApps.setOnClickListener {
            filterApps(showAll = false, showSystemApps = false)
        }
        
        binding.btnShowSystemApps.setOnClickListener {
            filterApps(showAll = false, showSystemApps = true)
        }
    }

    private fun loadApps() {
        binding.progressBar.visibility = View.VISIBLE
        binding.recyclerView.visibility = View.GONE
        
        coroutineScope.launch {
            try {
                val apps = withContext(Dispatchers.IO) {
                    loadInstalledApps()
                }
                
                allApps = apps
                filterApps(showAll = false, showSystemApps = false) // Show user apps by default
                
            } catch (e: Exception) {
                Toast.makeText(this@AppSelectionActivity, "Error loading apps: ${e.message}", Toast.LENGTH_LONG).show()
            } finally {
                binding.progressBar.visibility = View.GONE
                binding.recyclerView.visibility = View.VISIBLE
            }
        }
    }

    private suspend fun loadInstalledApps(): List<AppInfo> {
        val packageManager = packageManager
        val installedApps = packageManager.getInstalledApplications(PackageManager.GET_META_DATA)
        val blockedApps = blockedAppsManager.getBlockedApps().toSet()
        
        return installedApps
            .filter { appInfo ->
                // Filter out our own app and some system essentials
                appInfo.packageName != packageName &&
                appInfo.packageName != "android" &&
                appInfo.packageName != "com.android.systemui"
            }
            .map { appInfo ->
                AppInfo(
                    packageName = appInfo.packageName,
                    appName = packageManager.getApplicationLabel(appInfo).toString(),
                    icon = packageManager.getApplicationIcon(appInfo),
                    isSystemApp = (appInfo.flags and ApplicationInfo.FLAG_SYSTEM) != 0,
                    isBlocked = blockedApps.contains(appInfo.packageName)
                )
            }
            .sortedBy { it.appName.lowercase() }
    }

    private fun filterApps(showAll: Boolean, showSystemApps: Boolean, searchQuery: String = "") {
        filteredApps = allApps.filter { app ->
            val matchesFilter = when {
                showAll -> true
                showSystemApps -> app.isSystemApp
                else -> !app.isSystemApp
            }
            
            val matchesSearch = if (searchQuery.isBlank()) {
                true
            } else {
                app.appName.contains(searchQuery, ignoreCase = true) ||
                app.packageName.contains(searchQuery, ignoreCase = true)
            }
            
            matchesFilter && matchesSearch
        }
        
        appListAdapter.submitList(filteredApps)
        
        // Update button states
        binding.btnShowAll.isSelected = showAll
        binding.btnShowUserApps.isSelected = !showAll && !showSystemApps
        binding.btnShowSystemApps.isSelected = showSystemApps
        
        // Update count
        binding.tvAppCount.text = "${filteredApps.size} apps"
    }

    private fun handleAppSelectionChanged(appInfo: AppInfo, isChecked: Boolean) {
        if (isChecked) {
            // Add to blocked apps
            if (isSystemApp(appInfo.packageName)) {
                showSystemAppWarning(appInfo) {
                    blockedAppsManager.addBlockedApp(appInfo.packageName)
                    appInfo.isBlocked = true
                    updateAppInList(appInfo)
                }
            } else {
                blockedAppsManager.addBlockedApp(appInfo.packageName)
                appInfo.isBlocked = true
                updateAppInList(appInfo)
            }
        } else {
            // Remove from blocked apps
            blockedAppsManager.removeBlockedApp(appInfo.packageName)
            appInfo.isBlocked = false
            updateAppInList(appInfo)
        }
    }

    private fun isSystemApp(packageName: String): Boolean {
        return try {
            val appInfo = packageManager.getApplicationInfo(packageName, 0)
            (appInfo.flags and ApplicationInfo.FLAG_SYSTEM) != 0
        } catch (e: PackageManager.NameNotFoundException) {
            false
        }
    }

    private fun showSystemAppWarning(appInfo: AppInfo, onConfirm: () -> Unit) {
        AlertDialog.Builder(this)
            .setTitle("Warning: System App")
            .setMessage("${appInfo.appName} is a system app. Blocking it may cause device instability or prevent normal operation. Are you sure you want to block it?")
            .setPositiveButton("Block Anyway") { _, _ ->
                onConfirm()
            }
            .setNegativeButton("Cancel") { _, _ ->
                // Reset checkbox state
                appListAdapter.notifyItemChanged(filteredApps.indexOf(appInfo))
            }
            .show()
    }

    private fun updateAppInList(appInfo: AppInfo) {
        val index = filteredApps.indexOf(appInfo)
        if (index >= 0) {
            appListAdapter.notifyItemChanged(index)
        }
        
        // Also update in allApps list
        val allIndex = allApps.indexOf(appInfo)
        if (allIndex >= 0) {
            allApps[allIndex].isBlocked = appInfo.isBlocked
        }
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.app_selection_menu, menu)
        
        // Setup search
        val searchItem = menu.findItem(R.id.action_search)
        val searchView = searchItem.actionView as SearchView
        
        searchView.setOnQueryTextListener(object : SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(query: String?): Boolean {
                return false
            }
            
            override fun onQueryTextChange(newText: String?): Boolean {
                val currentShowAll = binding.btnShowAll.isSelected
                val currentShowSystem = binding.btnShowSystemApps.isSelected
                filterApps(currentShowAll, currentShowSystem, newText ?: "")
                return true
            }
        })
        
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            R.id.action_select_all -> {
                selectAllVisibleApps()
                true
            }
            R.id.action_deselect_all -> {
                deselectAllVisibleApps()
                true
            }
            R.id.action_blocked_count -> {
                showBlockedAppsCount()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun selectAllVisibleApps() {
        AlertDialog.Builder(this)
            .setTitle("Select All Apps")
            .setMessage("This will block all ${filteredApps.size} visible apps. Are you sure?")
            .setPositiveButton("Select All") { _, _ ->
                filteredApps.forEach { app ->
                    if (!app.isBlocked) {
                        blockedAppsManager.addBlockedApp(app.packageName)
                        app.isBlocked = true
                    }
                }
                appListAdapter.notifyDataSetChanged()
                Toast.makeText(this, "Selected ${filteredApps.size} apps", Toast.LENGTH_SHORT).show()
            }
            .setNegativeButton("Cancel", null)
            .show()
    }

    private fun deselectAllVisibleApps() {
        val blockedCount = filteredApps.count { it.isBlocked }
        if (blockedCount == 0) {
            Toast.makeText(this, "No apps to deselect", Toast.LENGTH_SHORT).show()
            return
        }
        
        AlertDialog.Builder(this)
            .setTitle("Deselect All Apps")
            .setMessage("This will unblock all $blockedCount blocked apps in the current view. Are you sure?")
            .setPositiveButton("Deselect All") { _, _ ->
                filteredApps.forEach { app ->
                    if (app.isBlocked) {
                        blockedAppsManager.removeBlockedApp(app.packageName)
                        app.isBlocked = false
                    }
                }
                appListAdapter.notifyDataSetChanged()
                Toast.makeText(this, "Deselected $blockedCount apps", Toast.LENGTH_SHORT).show()
            }
            .setNegativeButton("Cancel", null)
            .show()
    }

    private fun showBlockedAppsCount() {
        val totalBlocked = blockedAppsManager.getBlockedApps().size
        val visibleBlocked = filteredApps.count { it.isBlocked }
        
        AlertDialog.Builder(this)
            .setTitle("Blocked Apps Summary")
            .setMessage("Total blocked apps: $totalBlocked\nBlocked apps in current view: $visibleBlocked")
            .setPositiveButton("OK", null)
            .show()
    }

    override fun onDestroy() {
        super.onDestroy()
        coroutineScope.cancel()
    }
}

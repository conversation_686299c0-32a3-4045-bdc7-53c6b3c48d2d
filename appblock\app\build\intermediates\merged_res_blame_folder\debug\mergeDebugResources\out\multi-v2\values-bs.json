{"logs": [{"outputFile": "com.appblock.app-mergeDebugResources-54:/values-bs/values-bs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\78c237f2989eef627b8efaab812c395e\\transformed\\core-1.10.1\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,457,561,663,780", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "148,250,348,452,556,658,775,876"}, "to": {"startLines": "39,40,41,42,43,44,45,110", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3539,3637,3739,3837,3941,4045,4147,9499", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "3632,3734,3832,3936,4040,4142,4259,9595"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6034af7fb018680d3c013049f167ec16\\transformed\\preference-1.2.1\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,263,346,484,653,740", "endColumns": "70,86,82,137,168,86,82", "endOffsets": "171,258,341,479,648,735,818"}, "to": {"startLines": "49,51,106,108,111,112,113", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4567,4704,9111,9274,9600,9769,9856", "endColumns": "70,86,82,137,168,86,82", "endOffsets": "4633,4786,9189,9407,9764,9851,9934"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\aa3c339c6f6dd0717546ddbb353d969b\\transformed\\navigation-ui-2.6.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,158", "endColumns": "102,124", "endOffsets": "153,278"}, "to": {"startLines": "104,105", "startColumns": "4,4", "startOffsets": "8883,8986", "endColumns": "102,124", "endOffsets": "8981,9106"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\94c90974a14a16e04a1c279783b656e5\\transformed\\material-1.9.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,327,406,486,568,670,764,860,986,1067,1133,1225,1302,1365,1473,1533,1599,1655,1726,1786,1840,1959,2016,2078,2132,2207,2331,2419,2502,2647,2732,2818,2906,2960,3014,3080,3154,3232,3319,3391,3468,3541,3611,3704,3776,3868,3964,4038,4114,4210,4263,4330,4417,4504,4566,4630,4693,4801,4903,5004,5109,5167,5225", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68", "endColumns": "12,78,79,81,101,93,95,125,80,65,91,76,62,107,59,65,55,70,59,53,118,56,61,53,74,123,87,82,144,84,85,87,53,53,65,73,77,86,71,76,72,69,92,71,91,95,73,75,95,52,66,86,86,61,63,62,107,101,100,104,57,57,79", "endOffsets": "322,401,481,563,665,759,855,981,1062,1128,1220,1297,1360,1468,1528,1594,1650,1721,1781,1835,1954,2011,2073,2127,2202,2326,2414,2497,2642,2727,2813,2901,2955,3009,3075,3149,3227,3314,3386,3463,3536,3606,3699,3771,3863,3959,4033,4109,4205,4258,4325,4412,4499,4561,4625,4688,4796,4898,4999,5104,5162,5220,5300"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,50,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3102,3181,3261,3343,3445,4264,4360,4486,4638,4791,4883,4960,5023,5131,5191,5257,5313,5384,5444,5498,5617,5674,5736,5790,5865,5989,6077,6160,6305,6390,6476,6564,6618,6672,6738,6812,6890,6977,7049,7126,7199,7269,7362,7434,7526,7622,7696,7772,7868,7921,7988,8075,8162,8224,8288,8351,8459,8561,8662,8767,8825,9194", "endLines": "6,34,35,36,37,38,46,47,48,50,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,107", "endColumns": "12,78,79,81,101,93,95,125,80,65,91,76,62,107,59,65,55,70,59,53,118,56,61,53,74,123,87,82,144,84,85,87,53,53,65,73,77,86,71,76,72,69,92,71,91,95,73,75,95,52,66,86,86,61,63,62,107,101,100,104,57,57,79", "endOffsets": "372,3176,3256,3338,3440,3534,4355,4481,4562,4699,4878,4955,5018,5126,5186,5252,5308,5379,5439,5493,5612,5669,5731,5785,5860,5984,6072,6155,6300,6385,6471,6559,6613,6667,6733,6807,6885,6972,7044,7121,7194,7264,7357,7429,7521,7617,7691,7767,7863,7916,7983,8070,8157,8219,8283,8346,8454,8556,8657,8762,8820,8878,9269"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\01183fe8ec5b70d0914d8fb6bc2ec50c\\transformed\\appcompat-1.6.1\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,323,430,516,620,742,827,909,1000,1093,1188,1282,1382,1475,1570,1665,1756,1847,1935,2038,2142,2243,2348,2462,2565,2734,2830", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,100,104,113,102,168,95,86", "endOffsets": "221,318,425,511,615,737,822,904,995,1088,1183,1277,1377,1470,1565,1660,1751,1842,1930,2033,2137,2238,2343,2457,2560,2729,2825,2912"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "377,498,595,702,788,892,1014,1099,1181,1272,1365,1460,1554,1654,1747,1842,1937,2028,2119,2207,2310,2414,2515,2620,2734,2837,3006,9412", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,100,104,113,102,168,95,86", "endOffsets": "493,590,697,783,887,1009,1094,1176,1267,1360,1455,1549,1649,1742,1837,1932,2023,2114,2202,2305,2409,2510,2615,2729,2832,3001,3097,9494"}}]}]}
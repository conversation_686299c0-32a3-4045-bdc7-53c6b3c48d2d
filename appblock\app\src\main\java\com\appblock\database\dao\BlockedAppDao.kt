package com.appblock.database.dao

import androidx.room.*
import com.appblock.database.entity.BlockedAppEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface BlockedAppDao {
    
    @Query("SELECT * FROM blocked_apps WHERE isBlocked = 1")
    fun getAllBlockedApps(): Flow<List<BlockedAppEntity>>
    
    @Query("SELECT * FROM blocked_apps WHERE packageName = :packageName")
    suspend fun getBlockedApp(packageName: String): BlockedAppEntity?
    
    @Query("SELECT * FROM blocked_apps WHERE isBlocked = 1 AND packageName = :packageName")
    suspend fun isAppBlocked(packageName: String): BlockedAppEntity?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertBlockedApp(app: BlockedAppEntity)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertBlockedApps(apps: List<BlockedAppEntity>)
    
    @Update
    suspend fun updateBlockedApp(app: BlockedAppEntity)
    
    @Delete
    suspend fun deleteBlockedApp(app: BlockedAppEntity)
    
    @Query("DELETE FROM blocked_apps WHERE packageName = :packageName")
    suspend fun deleteBlockedAppByPackage(packageName: String)
    
    @Query("DELETE FROM blocked_apps")
    suspend fun deleteAllBlockedApps()
    
    @Query("SELECT COUNT(*) FROM blocked_apps WHERE isBlocked = 1")
    suspend fun getBlockedAppsCount(): Int
    
    // Get apps that need to be synced with server
    @Query("SELECT * FROM blocked_apps WHERE syncedWithServer = 0")
    suspend fun getUnsyncedApps(): List<BlockedAppEntity>
}
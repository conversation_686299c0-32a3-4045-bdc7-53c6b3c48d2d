<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\appblock\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\appblock\app\src\main\res"><file name="block_card_background" path="C:\Users\<USER>\Desktop\appblock\app\src\main\res\drawable\block_card_background.xml" qualifiers="" type="drawable"/><file name="ic_access_time" path="C:\Users\<USER>\Desktop\appblock\app\src\main\res\drawable\ic_access_time.xml" qualifiers="" type="drawable"/><file name="ic_add" path="C:\Users\<USER>\Desktop\appblock\app\src\main\res\drawable\ic_add.xml" qualifiers="" type="drawable"/><file name="ic_admin_panel_settings" path="C:\Users\<USER>\Desktop\appblock\app\src\main\res\drawable\ic_admin_panel_settings.xml" qualifiers="" type="drawable"/><file name="ic_android" path="C:\Users\<USER>\Desktop\appblock\app\src\main\res\drawable\ic_android.xml" qualifiers="" type="drawable"/><file name="ic_block" path="C:\Users\<USER>\Desktop\appblock\app\src\main\res\drawable\ic_block.xml" qualifiers="" type="drawable"/><file name="ic_deselect_all" path="C:\Users\<USER>\Desktop\appblock\app\src\main\res\drawable\ic_deselect_all.xml" qualifiers="" type="drawable"/><file name="ic_home" path="C:\Users\<USER>\Desktop\appblock\app\src\main\res\drawable\ic_home.xml" qualifiers="" type="drawable"/><file name="ic_info" path="C:\Users\<USER>\Desktop\appblock\app\src\main\res\drawable\ic_info.xml" qualifiers="" type="drawable"/><file name="ic_list" path="C:\Users\<USER>\Desktop\appblock\app\src\main\res\drawable\ic_list.xml" qualifiers="" type="drawable"/><file name="ic_lock" path="C:\Users\<USER>\Desktop\appblock\app\src\main\res\drawable\ic_lock.xml" qualifiers="" type="drawable"/><file name="ic_monitor" path="C:\Users\<USER>\Desktop\appblock\app\src\main\res\drawable\ic_monitor.xml" qualifiers="" type="drawable"/><file name="ic_search" path="C:\Users\<USER>\Desktop\appblock\app\src\main\res\drawable\ic_search.xml" qualifiers="" type="drawable"/><file name="ic_security" path="C:\Users\<USER>\Desktop\appblock\app\src\main\res\drawable\ic_security.xml" qualifiers="" type="drawable"/><file name="ic_select_all" path="C:\Users\<USER>\Desktop\appblock\app\src\main\res\drawable\ic_select_all.xml" qualifiers="" type="drawable"/><file name="ic_settings" path="C:\Users\<USER>\Desktop\appblock\app\src\main\res\drawable\ic_settings.xml" qualifiers="" type="drawable"/><file name="ic_shield" path="C:\Users\<USER>\Desktop\appblock\app\src\main\res\drawable\ic_shield.xml" qualifiers="" type="drawable"/><file name="pin_button_background" path="C:\Users\<USER>\Desktop\appblock\app\src\main\res\drawable\pin_button_background.xml" qualifiers="" type="drawable"/><file name="system_app_badge" path="C:\Users\<USER>\Desktop\appblock\app\src\main\res\drawable\system_app_badge.xml" qualifiers="" type="drawable"/><file name="activity_app_selection" path="C:\Users\<USER>\Desktop\appblock\app\src\main\res\layout\activity_app_selection.xml" qualifiers="" type="layout"/><file name="activity_block_overlay" path="C:\Users\<USER>\Desktop\appblock\app\src\main\res\layout\activity_block_overlay.xml" qualifiers="" type="layout"/><file name="activity_main" path="C:\Users\<USER>\Desktop\appblock\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_pin_auth" path="C:\Users\<USER>\Desktop\appblock\app\src\main\res\layout\activity_pin_auth.xml" qualifiers="" type="layout"/><file name="activity_website_selection" path="C:\Users\<USER>\Desktop\appblock\app\src\main\res\layout\activity_website_selection.xml" qualifiers="" type="layout"/><file name="block_overlay" path="C:\Users\<USER>\Desktop\appblock\app\src\main\res\layout\block_overlay.xml" qualifiers="" type="layout"/><file name="dialog_pin_setup" path="C:\Users\<USER>\Desktop\appblock\app\src\main\res\layout\dialog_pin_setup.xml" qualifiers="" type="layout"/><file name="dialog_pin_verify" path="C:\Users\<USER>\Desktop\appblock\app\src\main\res\layout\dialog_pin_verify.xml" qualifiers="" type="layout"/><file name="item_app" path="C:\Users\<USER>\Desktop\appblock\app\src\main\res\layout\item_app.xml" qualifiers="" type="layout"/><file name="app_selection_menu" path="C:\Users\<USER>\Desktop\appblock\app\src\main\res\menu\app_selection_menu.xml" qualifiers="" type="menu"/><file name="main_menu" path="C:\Users\<USER>\Desktop\appblock\app\src\main\res\menu\main_menu.xml" qualifiers="" type="menu"/><file path="C:\Users\<USER>\Desktop\appblock\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="primary">#FF2196F3</color><color name="primary_dark">#FF1976D2</color><color name="accent">#FFFF5722</color><color name="block_overlay_background">#E6000000</color><color name="block_card_background">#FFFFFF</color><color name="blocked_app_background">#FFEBEE</color><color name="success_green">#FF4CAF50</color><color name="warning_orange">#FFFF9800</color><color name="error_red">#FFF44336</color><color name="pin_button_background">#FFF5F5F5</color><color name="pin_button_pressed">#FFE0E0E0</color></file><file path="C:\Users\<USER>\Desktop\appblock\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">App Block</string><string name="permissions">Permissions</string><string name="blocked_apps">Blocked Apps</string><string name="settings">Settings</string><string name="app_blocking">App Blocking</string><string name="setup_pin">Setup PIN</string><string name="device_admin">Device Admin</string><string name="select_apps_to_block">Select Apps to Block</string><string name="show_all">All</string><string name="show_user_apps">User</string><string name="show_system_apps">System</string><string name="search_apps">Search apps...</string><string name="app_blocked_title">App Blocked</string><string name="app_blocked_message">This app is currently blocked by parental controls</string><string name="enter_pin">Enter PIN</string><string name="go_home">Go Home</string><string name="request_time">Request Time</string><string name="blocked_websites">Blocked Websites</string><string name="network_blocking">Network-Level Blocking</string><string name="add_website">Add Website</string><string name="website_hint">Enter website (e.g., facebook.com)</string><string name="enter_parent_pin">Enter Parent PIN</string><string name="verify">Verify</string><string name="cancel">Cancel</string><string name="clear">Clear</string><string name="accessibility_service_description">App Block uses this service to monitor which apps are opened and block restricted apps by showing an overlay screen. This service does not collect or store any personal data.</string><string name="notification_title">App Block Active</string><string name="notification_text">Parental controls are monitoring app usage</string><string name="action_search">Search</string><string name="action_select_all">Select All</string><string name="action_deselect_all">Deselect All</string><string name="action_blocked_count">Blocked Count</string><string name="action_logs">Logs</string><string name="action_about">About</string><string name="ok">OK</string><string name="yes">Yes</string><string name="no">No</string><string name="enable">Enable</string><string name="disable">Disable</string><string name="save">Save</string><string name="delete">Delete</string></file><file path="C:\Users\<USER>\Desktop\appblock\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.AppBlock" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor" ns1:targetApi="l">?attr/colorPrimaryVariant</item>
        
    </style><style name="Theme.AppBlock.Fullscreen" parent="Theme.AppBlock">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@color/block_overlay_background</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowAnimationStyle">@null</item>
    </style><style name="Theme.AppBlock.Dialog" parent="Theme.Material3.DayNight.Dialog">
        <item name="colorPrimary">@color/primary</item>
        <item name="android:windowBackground">@android:color/transparent</item>
    </style><style name="PinButtonStyle">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">60dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:layout_margin">4dp</item>
        <item name="android:background">@drawable/pin_button_background</item>
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/black</item>
    </style></file><file name="accessibility_service_config" path="C:\Users\<USER>\Desktop\appblock\app\src\main\res\xml\accessibility_service_config.xml" qualifiers="" type="xml"/><file name="backup_rules" path="C:\Users\<USER>\Desktop\appblock\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\Desktop\appblock\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="device_admin" path="C:\Users\<USER>\Desktop\appblock\app\src\main\res\xml\device_admin.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\appblock\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\appblock\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\appblock\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\appblock\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>
package com.appblock.api

import com.appblock.model.*
import retrofit2.Response
import retrofit2.http.*

interface ApiService {
    
    @POST("users/use-code/{code}")
    suspend fun useRegistrationCode(
        @Path("code") code: String,
        @Body request: RegistrationRequest
    ): Response<ApiResponse<AuthResponse>>
    
    @POST("app-usage/upload")
    suspend fun uploadAppUsage(
        @Body appUsageDataList: List<AppUsageData>
    ): Response<ApiResponse<Void>>
    
    @GET("child-apps/blocked")
    suspend fun getBlockedApps(): Response<ApiResponse<List<BlockedApp>>>
    
    @POST("auth/refresh-token")
    suspend fun refreshToken(
        @Body request: RefreshTokenRequest
    ): Response<ApiResponse<AuthResponse>>
    
    @GET("auth/me")
    suspend fun getCurrentUser(): Response<ApiResponse<User>>
}

data class RefreshTokenRequest(
    val refreshToken: String
)
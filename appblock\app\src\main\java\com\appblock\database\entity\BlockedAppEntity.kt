package com.appblock.database.entity

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "blocked_apps")
data class BlockedAppEntity(
    @PrimaryKey
    val packageName: String,
    val appName: String,
    val isBlocked: Boolean,
    val timeLimit: Int? = null, // in minutes
    val startTime: String? = null, // HH:mm format
    val endTime: String? = null, // HH:mm format
    val lastUpdated: Long = System.currentTimeMillis(),
    val syncedWithServer: Boolean = true
)
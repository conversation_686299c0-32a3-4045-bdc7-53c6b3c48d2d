package com.appblock.ui

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.widget.Button
import android.widget.EditText
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.appblock.R
import com.appblock.auth.AuthManager
import kotlinx.coroutines.launch

class RegistrationActivity : AppCompatActivity() {
    
    companion object {
        private const val TAG = "RegistrationActivity"
    }
    
    private lateinit var authManager: AuthManager
    private lateinit var codeEditText: EditText
    private lateinit var registerButton: Button
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_registration)
        
        authManager = AuthManager.getInstance(this)
        
        // Check if already authenticated
        if (authManager.isAuthenticated()) {
            navigateToMain()
            return
        }
        
        initViews()
        setupClickListeners()
    }
    
    private fun initViews() {
        codeEditText = findViewById(R.id.editTextCode)
        registerButton = findViewById(R.id.buttonRegister)
    }
    
    private fun setupClickListeners() {
        registerButton.setOnClickListener {
            val code = codeEditText.text.toString().trim()
            if (code.isNotEmpty()) {
                registerDevice(code)
            } else {
                Toast.makeText(this, "Please enter registration code", Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    private fun registerDevice(code: String) {
        registerButton.isEnabled = false
        registerButton.text = "Registering..."
        
        lifecycleScope.launch {
            val result = authManager.registerDevice(code)
            
            result.onSuccess {
                Toast.makeText(this@RegistrationActivity, "Registration successful!", Toast.LENGTH_SHORT).show()
                navigateToMain()
            }.onFailure { error ->
                Toast.makeText(this@RegistrationActivity, "Registration failed: ${error.message}", Toast.LENGTH_LONG).show()
                registerButton.isEnabled = true
                registerButton.text = "Register"
            }
        }
    }
    
    private fun navigateToMain() {
        startActivity(Intent(this, MainActivity::class.java))
        finish()
    }
}
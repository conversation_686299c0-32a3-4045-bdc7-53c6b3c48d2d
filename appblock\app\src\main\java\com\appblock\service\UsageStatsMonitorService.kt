package com.appblock.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.app.usage.UsageEvents
import android.app.usage.UsageStatsManager
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.util.Log
import androidx.core.app.NotificationCompat
// import com.appblock.R // Will be generated
import com.appblock.data.BlockedAppsManager
// import com.appblock.ui.BlockOverlayActivity // Removed for core functionality
import com.appblock.utils.PermissionUtils
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService
import java.util.concurrent.TimeUnit
import kotlinx.coroutines.runBlocking

class UsageStatsMonitorService : Service() {

    companion object {
        private const val TAG = "UsageStatsMonitor"
        private const val NOTIFICATION_ID = 1002
        private const val CHANNEL_ID = "usage_stats_monitor"
        private const val POLLING_INTERVAL_SECONDS = 5L

        @Volatile
        private var instance: UsageStatsMonitorService? = null

        fun getInstance(): UsageStatsMonitorService? = instance

        fun isServiceRunning(): Boolean = instance != null
    }

    private lateinit var usageStatsManager: UsageStatsManager
    private lateinit var blockedAppsManager: BlockedAppsManager
    private lateinit var scheduler: ScheduledExecutorService
    private val mainHandler = Handler(Looper.getMainLooper())

    private var lastCheckTime = 0L
    private var currentForegroundApp: String? = null

    override fun onCreate() {
        super.onCreate()
        instance = this

        usageStatsManager = getSystemService(Context.USAGE_STATS_SERVICE) as UsageStatsManager
        blockedAppsManager = BlockedAppsManager.getInstance(this)
        scheduler = Executors.newSingleThreadScheduledExecutor()

        Log.d(TAG, "UsageStatsMonitorService created")
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "UsageStatsMonitorService started")

        startForegroundService()
        startMonitoring()

        return START_STICKY // Restart if killed
    }

    override fun onBind(intent: Intent?): IBinder? = null

    private fun startMonitoring() {
        // Check if we have usage stats permission
        if (!PermissionUtils.hasUsageStatsPermission(this)) {
            Log.w(TAG, "Usage stats permission not granted")
            return
        }

        lastCheckTime = System.currentTimeMillis() - TimeUnit.SECONDS.toMillis(10)

        // Start periodic monitoring
        scheduler.scheduleAtFixedRate(
            { monitorForegroundApp() },
            0,
            POLLING_INTERVAL_SECONDS,
            TimeUnit.SECONDS
        )

        Log.d(TAG, "Usage stats monitoring started")
    }

    private fun monitorForegroundApp() {
        try {
            val currentTime = System.currentTimeMillis()
            val events = usageStatsManager.queryEvents(lastCheckTime, currentTime)

            var latestEvent: UsageEvents.Event? = null
            val event = UsageEvents.Event()

            // Find the most recent MOVE_TO_FOREGROUND event
            while (events.hasNextEvent()) {
                events.getNextEvent(event)

                if (event.eventType == UsageEvents.Event.MOVE_TO_FOREGROUND) {
                    if (latestEvent == null || event.timeStamp > latestEvent.timeStamp) {
                        latestEvent = event
                    }
                }
            }

            latestEvent?.let { foregroundEvent ->
                val packageName = foregroundEvent.packageName

                if (packageName != currentForegroundApp) {
                    currentForegroundApp = packageName
                    Log.d(TAG, "Foreground app changed to: $packageName")

                    // Check if this app should be blocked
                    if (runBlocking { blockedAppsManager.isAppBlocked(packageName) }) {
                        Log.d(TAG, "Detected blocked app via UsageStats: $packageName")
                        handleBlockedApp(packageName)
                    }
                }
            }

            lastCheckTime = currentTime

        } catch (e: Exception) {
            Log.e(TAG, "Error monitoring usage stats", e)
        }
    }

    private fun handleBlockedApp(packageName: String) {
        // Only act if accessibility service is not running
        val accessibilityService = AppBlockAccessibilityService.getInstance()

        if (accessibilityService == null || !AppBlockAccessibilityService.isServiceRunning()) {
            Log.d(TAG, "Accessibility service not available, using UsageStats fallback")
            blockAppViaFallback(packageName)
        } else {
            Log.d(TAG, "Accessibility service is running, letting it handle blocking for: $packageName")
            // Let accessibility service handle it - don't interfere
        }
    }

    private fun blockAppViaFallback(packageName: String) {
        mainHandler.post {
            Log.d(TAG, "FALLBACK BLOCKING: $packageName (Accessibility service not available)")
            // Fallback: just go to home when blocked app detected
            val homeIntent = Intent(Intent.ACTION_MAIN).apply {
                addCategory(Intent.CATEGORY_HOME)
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            startActivity(homeIntent)
            Log.d(TAG, "Blocked app $packageName detected, sent to home via fallback")
        }
    }

    private fun startForegroundService() {
        createNotificationChannel()

        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("App Monitor Active")
            .setContentText("Monitoring app usage in background")
            .setSmallIcon(android.R.drawable.ic_menu_view)
            .setOngoing(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()

        startForeground(NOTIFICATION_ID, notification)
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "App Monitor Service",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Background monitoring of app usage"
                setShowBadge(false)
            }

            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }

    fun getCurrentForegroundApp(): String? = currentForegroundApp

    fun getAppUsageTime(packageName: String, timeRangeMs: Long): Long {
        try {
            val endTime = System.currentTimeMillis()
            val startTime = endTime - timeRangeMs

            val usageStats = usageStatsManager.queryUsageStats(
                UsageStatsManager.INTERVAL_DAILY,
                startTime,
                endTime
            )

            return usageStats.find { it.packageName == packageName }?.totalTimeInForeground ?: 0L

        } catch (e: Exception) {
            Log.e(TAG, "Error getting usage time for $packageName", e)
            return 0L
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        instance = null

        scheduler.shutdown()
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow()
            }
        } catch (e: InterruptedException) {
            scheduler.shutdownNow()
        }

        Log.d(TAG, "UsageStatsMonitorService destroyed")
    }
}

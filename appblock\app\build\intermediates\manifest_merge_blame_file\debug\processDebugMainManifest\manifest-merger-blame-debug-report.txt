1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.appblock"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <!-- Required permissions for app blocking functionality -->
12    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS" />
12-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:6:5-78
12-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:6:22-75
13    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
13-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:7:5-78
13-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:7:22-75
14    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
14-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:8:5-81
14-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:8:22-78
15    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
15-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:9:5-77
15-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:9:22-74
16    <uses-permission android:name="android.permission.WAKE_LOCK" />
16-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:10:5-68
16-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:10:22-65
17    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
17-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:11:5-77
17-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:11:22-74
18    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
18-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:12:5-77
18-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:12:22-74
19    <!-- VPN permissions for network-level website blocking -->
20    <uses-permission android:name="android.permission.BIND_VPN_SERVICE" />
20-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:14:5-75
20-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:14:22-72
21    <uses-permission android:name="android.permission.INTERNET" />
21-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:15:5-67
21-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:15:22-64
22    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
22-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:5-79
22-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:24:22-76
23
24    <permission
24-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78c237f2989eef627b8efaab812c395e\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
25        android:name="com.appblock.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
25-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78c237f2989eef627b8efaab812c395e\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
26        android:protectionLevel="signature" />
26-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78c237f2989eef627b8efaab812c395e\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
27
28    <uses-permission android:name="com.appblock.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
28-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78c237f2989eef627b8efaab812c395e\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
28-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78c237f2989eef627b8efaab812c395e\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
29
30    <application
30-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:17:5-109:19
31        android:allowBackup="true"
31-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:18:9-35
32        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
32-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\78c237f2989eef627b8efaab812c395e\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
33        android:debuggable="true"
34        android:extractNativeLibs="true"
35        android:label="App Block"
35-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:19:9-34
36        android:supportsRtl="true"
36-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:20:9-35
37        android:theme="@android:style/Theme.Material.Light" >
37-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:21:9-60
38
39        <!-- Main Activity -->
40        <activity
40-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:25:9-34:20
41            android:name="com.appblock.MainActivity"
41-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:26:13-41
42            android:exported="true"
42-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:27:13-36
43            android:label="App Block" >
43-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:28:13-38
44
45            <!-- Removed theme reference -->
46            <intent-filter>
46-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:30:13-33:29
47                <action android:name="android.intent.action.MAIN" />
47-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:31:17-69
47-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:31:25-66
48
49                <category android:name="android.intent.category.LAUNCHER" />
49-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:32:17-77
49-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:32:27-74
50            </intent-filter>
51        </activity>
52
53        <!-- App Selection Activity -->
54        <activity
54-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:37:9-40:52
55            android:name="com.appblock.ui.SimpleAppSelectionActivity"
55-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:38:13-58
56            android:exported="false"
56-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:39:13-37
57            android:label="Select Apps to Block" />
57-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:40:13-49
58
59        <!-- Permission Setup Activity -->
60        <activity
60-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:43:9-46:47
61            android:name="com.appblock.ui.PermissionSetupActivity"
61-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:44:13-55
62            android:exported="false"
62-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:45:13-37
63            android:label="App Block Setup" />
63-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:46:13-44
64
65        <!-- Accessibility Service for app monitoring -->
66        <service
66-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:49:9-59:19
67            android:name="com.appblock.service.AppBlockAccessibilityService"
67-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:50:13-65
68            android:exported="false"
68-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:51:13-37
69            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
69-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:52:13-79
70            <intent-filter>
70-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:53:13-55:29
71                <action android:name="android.accessibilityservice.AccessibilityService" />
71-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:54:17-92
71-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:54:25-89
72            </intent-filter>
73
74            <meta-data
74-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:56:13-58:72
75                android:name="android.accessibilityservice"
75-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:57:17-60
76                android:resource="@xml/accessibility_service_config" />
76-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:58:17-69
77        </service>
78
79        <!-- Usage Stats Monitoring Service -->
80        <service
80-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:62:9-66:58
81            android:name="com.appblock.service.UsageStatsMonitorService"
81-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:63:13-61
82            android:exported="false"
82-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:64:13-37
83            android:foregroundServiceType="specialUse"
83-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:66:13-55
84            android:process=":monitor" />
84-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:65:13-39
85
86        <!-- VPN Service for network-level website blocking -->
87        <service
87-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:69:9-76:19
88            android:name="com.appblock.service.WebBlockVpnService"
88-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:70:13-55
89            android:exported="false"
89-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:71:13-37
90            android:permission="android.permission.BIND_VPN_SERVICE" >
90-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:72:13-69
91            <intent-filter>
91-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:73:13-75:29
92                <action android:name="android.net.VpnService" />
92-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:74:17-65
92-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:74:25-62
93            </intent-filter>
94        </service>
95
96        <!-- Device Admin Receiver -->
97        <receiver
97-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:79:9-90:20
98            android:name="com.appblock.receiver.AppBlockDeviceAdminReceiver"
98-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:80:13-65
99            android:exported="true"
99-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:81:13-36
100            android:permission="android.permission.BIND_DEVICE_ADMIN" >
100-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:82:13-70
101            <meta-data
101-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:83:13-85:56
102                android:name="android.app.device_admin"
102-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:84:17-56
103                android:resource="@xml/device_admin" />
103-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:85:17-53
104
105            <intent-filter>
105-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:86:13-89:29
106                <action android:name="android.app.action.DEVICE_ADMIN_ENABLED" />
106-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:87:17-82
106-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:87:25-79
107                <action android:name="android.app.action.DEVICE_ADMIN_DISABLED" />
107-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:88:17-83
107-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:88:25-80
108            </intent-filter>
109        </receiver>
110
111        <!-- Boot Receiver -->
112        <receiver
112-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:93:9-102:20
113            android:name="com.appblock.receiver.BootReceiver"
113-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:94:13-50
114            android:exported="true" >
114-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:95:13-36
115            <intent-filter android:priority="1000" >
115-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:96:13-101:29
115-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:96:28-51
116                <action android:name="android.intent.action.BOOT_COMPLETED" />
116-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:97:17-79
116-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:97:25-76
117                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
117-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:98:17-84
117-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:98:25-81
118                <action android:name="android.intent.action.PACKAGE_REPLACED" />
118-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:99:17-81
118-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:99:25-78
119
120                <data android:scheme="package" />
120-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:100:17-50
120-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:100:23-47
121            </intent-filter>
122        </receiver>
123
124        <!-- Permission Monitor Receiver -->
125        <receiver
125-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:105:9-107:40
126            android:name="com.appblock.receiver.PermissionMonitorReceiver"
126-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:106:13-63
127            android:exported="false" />
127-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:107:13-37
128
129        <provider
129-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f528ee55609a3282ae69f0023123ec06\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
130            android:name="androidx.startup.InitializationProvider"
130-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f528ee55609a3282ae69f0023123ec06\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
131            android:authorities="com.appblock.androidx-startup"
131-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f528ee55609a3282ae69f0023123ec06\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
132            android:exported="false" >
132-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f528ee55609a3282ae69f0023123ec06\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
133            <meta-data
133-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f528ee55609a3282ae69f0023123ec06\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
134                android:name="androidx.emoji2.text.EmojiCompatInitializer"
134-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f528ee55609a3282ae69f0023123ec06\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
135                android:value="androidx.startup" />
135-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f528ee55609a3282ae69f0023123ec06\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
136            <meta-data
136-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
137                android:name="androidx.work.WorkManagerInitializer"
137-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
138                android:value="androidx.startup" />
138-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
139            <meta-data
139-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b72165d7a1a5c3a50e357f4d5878d306\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
140                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
140-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b72165d7a1a5c3a50e357f4d5878d306\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
141                android:value="androidx.startup" />
141-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b72165d7a1a5c3a50e357f4d5878d306\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
142            <meta-data
142-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
143                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
143-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
144                android:value="androidx.startup" />
144-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
145        </provider>
146
147        <service
147-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
148            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
148-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
149            android:directBootAware="false"
149-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
150            android:enabled="@bool/enable_system_alarm_service_default"
150-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
151            android:exported="false" />
151-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
152        <service
152-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
153            android:name="androidx.work.impl.background.systemjob.SystemJobService"
153-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
154            android:directBootAware="false"
154-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
155            android:enabled="@bool/enable_system_job_service_default"
155-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
156            android:exported="true"
156-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
157            android:permission="android.permission.BIND_JOB_SERVICE" />
157-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
158        <service
158-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
159            android:name="androidx.work.impl.foreground.SystemForegroundService"
159-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
160            android:directBootAware="false"
160-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
161            android:enabled="@bool/enable_system_foreground_service_default"
161-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
162            android:exported="false" />
162-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
163
164        <receiver
164-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
165            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
165-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
166            android:directBootAware="false"
166-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
167            android:enabled="true"
167-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
168            android:exported="false" />
168-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
169        <receiver
169-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
170            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
170-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
171            android:directBootAware="false"
171-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
172            android:enabled="false"
172-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
173            android:exported="false" >
173-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
174            <intent-filter>
174-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
175                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
175-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
175-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
176                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
176-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
176-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
177            </intent-filter>
178        </receiver>
179        <receiver
179-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
180            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
180-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
181            android:directBootAware="false"
181-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
182            android:enabled="false"
182-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
183            android:exported="false" >
183-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
184            <intent-filter>
184-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
185                <action android:name="android.intent.action.BATTERY_OKAY" />
185-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
185-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
186                <action android:name="android.intent.action.BATTERY_LOW" />
186-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
186-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
187            </intent-filter>
188        </receiver>
189        <receiver
189-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
190            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
190-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
191            android:directBootAware="false"
191-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
192            android:enabled="false"
192-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
193            android:exported="false" >
193-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
194            <intent-filter>
194-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
195                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
195-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
195-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
196                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
196-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
196-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
197            </intent-filter>
198        </receiver>
199        <receiver
199-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
200            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
200-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
201            android:directBootAware="false"
201-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
202            android:enabled="false"
202-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
203            android:exported="false" >
203-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
204            <intent-filter>
204-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
205                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
205-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
205-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
206            </intent-filter>
207        </receiver>
208        <receiver
208-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
209            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
209-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
210            android:directBootAware="false"
210-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
211            android:enabled="false"
211-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
212            android:exported="false" >
212-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
213            <intent-filter>
213-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
214                <action android:name="android.intent.action.BOOT_COMPLETED" />
214-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:97:17-79
214-->C:\Users\<USER>\Desktop\appblock\app\src\main\AndroidManifest.xml:97:25-76
215                <action android:name="android.intent.action.TIME_SET" />
215-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
215-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
216                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
216-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
216-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
217            </intent-filter>
218        </receiver>
219        <receiver
219-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
220            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
220-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
221            android:directBootAware="false"
221-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
222            android:enabled="@bool/enable_system_alarm_service_default"
222-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
223            android:exported="false" >
223-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
224            <intent-filter>
224-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
225                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
225-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
225-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
226            </intent-filter>
227        </receiver>
228        <receiver
228-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
229            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
229-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
230            android:directBootAware="false"
230-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
231            android:enabled="true"
231-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
232            android:exported="true"
232-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
233            android:permission="android.permission.DUMP" >
233-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
234            <intent-filter>
234-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
235                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
235-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
235-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c2a4a781600edfb30116d4be623cdf8\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
236            </intent-filter>
237        </receiver>
238
239        <uses-library
239-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0836ce888dcd5275847a52d0eb6ce6f4\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
240            android:name="androidx.window.extensions"
240-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0836ce888dcd5275847a52d0eb6ce6f4\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
241            android:required="false" />
241-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0836ce888dcd5275847a52d0eb6ce6f4\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
242        <uses-library
242-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0836ce888dcd5275847a52d0eb6ce6f4\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
243            android:name="androidx.window.sidecar"
243-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0836ce888dcd5275847a52d0eb6ce6f4\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
244            android:required="false" />
244-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0836ce888dcd5275847a52d0eb6ce6f4\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
245
246        <service
246-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0011dcff6517cb4e1d70ad4d1beee19\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
247            android:name="androidx.room.MultiInstanceInvalidationService"
247-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0011dcff6517cb4e1d70ad4d1beee19\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
248            android:directBootAware="true"
248-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0011dcff6517cb4e1d70ad4d1beee19\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
249            android:exported="false" />
249-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e0011dcff6517cb4e1d70ad4d1beee19\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
250
251        <receiver
251-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
252            android:name="androidx.profileinstaller.ProfileInstallReceiver"
252-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
253            android:directBootAware="false"
253-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
254            android:enabled="true"
254-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
255            android:exported="true"
255-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
256            android:permission="android.permission.DUMP" >
256-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
257            <intent-filter>
257-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
258                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
258-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
258-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
259            </intent-filter>
260            <intent-filter>
260-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
261                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
261-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
261-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
262            </intent-filter>
263            <intent-filter>
263-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
264                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
264-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
264-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
265            </intent-filter>
266            <intent-filter>
266-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
267                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
267-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
267-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c69d24d4c24f9c97e02135b4bd4c63de\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
268            </intent-filter>
269        </receiver>
270    </application>
271
272</manifest>

package com.appblock.ui

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.*
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import com.appblock.R
import com.appblock.data.BlockedAppsManager
import kotlinx.coroutines.runBlocking
import com.google.android.material.floatingactionbutton.FloatingActionButton

class WebsiteSelectionActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "WebsiteSelectionActivity"
        
        // Common website categories for quick selection
        private val SOCIAL_MEDIA_SITES = listOf(
            "facebook.com", "instagram.com", "twitter.com", "x.com", "tiktok.com",
            "snapchat.com", "linkedin.com", "pinterest.com", "reddit.com"
        )
        
        private val ENTERTAINMENT_SITES = listOf(
            "youtube.com", "netflix.com", "hulu.com", "twitch.tv", "spotify.com",
            "disney.com", "hbo.com", "amazon.com/prime"
        )
        
        private val GAMING_SITES = listOf(
            "steam.com", "epicgames.com", "roblox.com", "minecraft.net",
            "fortnite.com", "ea.com", "ubisoft.com"
        )
        
        private val NEWS_SITES = listOf(
            "cnn.com", "bbc.com", "fox.com", "nytimes.com", "washingtonpost.com",
            "reuters.com", "ap.org"
        )
    }

    private lateinit var blockedAppsManager: BlockedAppsManager
    private lateinit var websiteListView: ListView
    private lateinit var searchEditText: EditText
    private lateinit var addWebsiteFab: FloatingActionButton
    private lateinit var categorySpinner: Spinner
    
    private var allBlockedWebsites = mutableListOf<String>()
    private var filteredWebsites = mutableListOf<String>()
    private lateinit var websiteAdapter: ArrayAdapter<String>

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        Log.d(TAG, "WebsiteSelectionActivity created")
        
        blockedAppsManager = BlockedAppsManager.getInstance(this)
        
        createUI()
        setupListeners()
        loadBlockedWebsites()
    }

    private fun createUI() {
        // Create main layout
        val mainLayout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(16, 16, 16, 16)
        }

        // Title
        val titleText = TextView(this).apply {
            text = "🌐 Blocked Websites"
            textSize = 24f
            setPadding(0, 0, 0, 16)
            gravity = android.view.Gravity.CENTER
        }
        mainLayout.addView(titleText)

        // Search box
        searchEditText = EditText(this).apply {
            hint = "Search blocked websites..."
            setPadding(16, 16, 16, 16)
        }
        mainLayout.addView(searchEditText)

        // Category selector
        val categoryLabel = TextView(this).apply {
            text = "Quick Add Categories:"
            textSize = 16f
            setPadding(0, 16, 0, 8)
        }
        mainLayout.addView(categoryLabel)

        categorySpinner = Spinner(this).apply {
            val categories = arrayOf(
                "Select Category...",
                "Social Media",
                "Entertainment", 
                "Gaming",
                "News"
            )
            adapter = ArrayAdapter(this@WebsiteSelectionActivity, android.R.layout.simple_spinner_item, categories).apply {
                setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
            }
        }
        mainLayout.addView(categorySpinner)

        // Website list
        websiteListView = ListView(this).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                0,
                1f
            )
        }
        mainLayout.addView(websiteListView)

        // Status text
        val statusText = TextView(this).apply {
            text = "Tap a website to remove it from blocked list\nUse + button to add new websites"
            textSize = 14f
            setPadding(0, 16, 0, 0)
            gravity = android.view.Gravity.CENTER
        }
        mainLayout.addView(statusText)

        setContentView(mainLayout)

        // Add floating action button
        addWebsiteFab = FloatingActionButton(this).apply {
            setImageResource(android.R.drawable.ic_input_add)
            setOnClickListener { showAddWebsiteDialog() }
        }
        
        // Position FAB (simplified positioning)
        val fabLayout = FrameLayout(this)
        fabLayout.addView(addWebsiteFab)
        
        // Replace content with frame layout containing both main layout and FAB
        val frameLayout = FrameLayout(this)
        frameLayout.addView(mainLayout)
        frameLayout.addView(fabLayout)
        setContentView(frameLayout)
    }

    private fun setupListeners() {
        // Search functionality
        searchEditText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                filterWebsites(s.toString())
            }
        })

        // Category selection
        categorySpinner.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                when (position) {
                    1 -> showCategoryDialog("Social Media", SOCIAL_MEDIA_SITES)
                    2 -> showCategoryDialog("Entertainment", ENTERTAINMENT_SITES)
                    3 -> showCategoryDialog("Gaming", GAMING_SITES)
                    4 -> showCategoryDialog("News", NEWS_SITES)
                }
                // Reset spinner to default
                if (position > 0) {
                    categorySpinner.setSelection(0)
                }
            }
            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }

        // Website list click to remove
        websiteListView.setOnItemClickListener { _, _, position, _ ->
            val website = filteredWebsites[position]
            showRemoveWebsiteDialog(website)
        }
    }

    private fun loadBlockedWebsites() {
        allBlockedWebsites.clear()
        allBlockedWebsites.addAll(blockedAppsManager.getBlockedWebsites())
        
        Log.d(TAG, "Loaded ${allBlockedWebsites.size} blocked websites")
        
        filterWebsites("")
    }

    private fun filterWebsites(query: String) {
        filteredWebsites.clear()
        
        if (query.isEmpty()) {
            filteredWebsites.addAll(allBlockedWebsites)
        } else {
            filteredWebsites.addAll(
                allBlockedWebsites.filter { 
                    it.contains(query, ignoreCase = true) 
                }
            )
        }
        
        // Sort alphabetically
        filteredWebsites.sort()
        
        // Update adapter
        if (!::websiteAdapter.isInitialized) {
            websiteAdapter = ArrayAdapter(
                this,
                android.R.layout.simple_list_item_1,
                filteredWebsites
            )
            websiteListView.adapter = websiteAdapter
        } else {
            websiteAdapter.notifyDataSetChanged()
        }
        
        Log.d(TAG, "Filtered to ${filteredWebsites.size} websites")
    }

    private fun showAddWebsiteDialog() {
        val editText = EditText(this).apply {
            hint = "Enter website (e.g., facebook.com)"
            setPadding(16, 16, 16, 16)
        }

        AlertDialog.Builder(this)
            .setTitle("Add Blocked Website")
            .setMessage("Enter the website domain to block:")
            .setView(editText)
            .setPositiveButton("Add") { _, _ ->
                val website = editText.text.toString().trim()
                if (website.isNotEmpty()) {
                    addWebsite(website)
                }
            }
            .setNegativeButton("Cancel", null)
            .show()
    }

    private fun showCategoryDialog(categoryName: String, websites: List<String>) {
        val availableWebsites = websites.filter { !allBlockedWebsites.contains(it) }
        
        if (availableWebsites.isEmpty()) {
            Toast.makeText(this, "All $categoryName websites are already blocked", Toast.LENGTH_SHORT).show()
            return
        }

        val checkedItems = BooleanArray(availableWebsites.size) { false }

        AlertDialog.Builder(this)
            .setTitle("Add $categoryName Websites")
            .setMultiChoiceItems(
                availableWebsites.toTypedArray(),
                checkedItems
            ) { _, which, isChecked ->
                checkedItems[which] = isChecked
            }
            .setPositiveButton("Add Selected") { _, _ ->
                val selectedWebsites = availableWebsites.filterIndexed { index, _ -> 
                    checkedItems[index] 
                }
                
                selectedWebsites.forEach { website ->
                    addWebsite(website)
                }
                
                if (selectedWebsites.isNotEmpty()) {
                    Toast.makeText(this, "Added ${selectedWebsites.size} websites", Toast.LENGTH_SHORT).show()
                }
            }
            .setNegativeButton("Cancel", null)
            .show()
    }

    private fun showRemoveWebsiteDialog(website: String) {
        AlertDialog.Builder(this)
            .setTitle("Remove Blocked Website")
            .setMessage("Remove '$website' from blocked websites?")
            .setPositiveButton("Remove") { _, _ ->
                removeWebsite(website)
            }
            .setNegativeButton("Cancel", null)
            .show()
    }

    private fun addWebsite(website: String) {
        val cleanWebsite = cleanWebsiteUrl(website)
        
        if (allBlockedWebsites.contains(cleanWebsite)) {
            Toast.makeText(this, "$cleanWebsite is already blocked", Toast.LENGTH_SHORT).show()
            return
        }

        blockedAppsManager.addBlockedWebsite(cleanWebsite)
        allBlockedWebsites.add(cleanWebsite)
        filterWebsites(searchEditText.text.toString())
        
        Log.d(TAG, "Added blocked website: $cleanWebsite")
        Toast.makeText(this, "Blocked $cleanWebsite", Toast.LENGTH_SHORT).show()
    }

    private fun removeWebsite(website: String) {
        blockedAppsManager.removeBlockedWebsite(website)
        allBlockedWebsites.remove(website)
        filterWebsites(searchEditText.text.toString())
        
        Log.d(TAG, "Removed blocked website: $website")
        Toast.makeText(this, "Unblocked $website", Toast.LENGTH_SHORT).show()
    }

    private fun cleanWebsiteUrl(url: String): String {
        var cleaned = url.trim().lowercase()
        
        // Remove protocol
        cleaned = cleaned.removePrefix("https://")
        cleaned = cleaned.removePrefix("http://")
        cleaned = cleaned.removePrefix("www.")
        
        // Remove trailing slash and path
        cleaned = cleaned.split("/")[0]
        
        return cleaned
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menu?.add(0, 1, 0, "Clear All")?.setShowAsAction(MenuItem.SHOW_AS_ACTION_NEVER)
        menu?.add(0, 2, 0, "Add Common Sites")?.setShowAsAction(MenuItem.SHOW_AS_ACTION_NEVER)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            1 -> {
                showClearAllDialog()
                true
            }
            2 -> {
                showAddCommonSitesDialog()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun showClearAllDialog() {
        AlertDialog.Builder(this)
            .setTitle("Clear All Blocked Websites")
            .setMessage("This will remove all ${allBlockedWebsites.size} blocked websites. Are you sure?")
            .setPositiveButton("Clear All") { _, _ ->
                allBlockedWebsites.forEach { website ->
                    blockedAppsManager.removeBlockedWebsite(website)
                }
                loadBlockedWebsites()
                Toast.makeText(this, "Cleared all blocked websites", Toast.LENGTH_SHORT).show()
            }
            .setNegativeButton("Cancel", null)
            .show()
    }

    private fun showAddCommonSitesDialog() {
        val commonSites = (SOCIAL_MEDIA_SITES + ENTERTAINMENT_SITES + GAMING_SITES).distinct()
        val availableSites = commonSites.filter { !allBlockedWebsites.contains(it) }
        
        if (availableSites.isEmpty()) {
            Toast.makeText(this, "All common sites are already blocked", Toast.LENGTH_SHORT).show()
            return
        }

        AlertDialog.Builder(this)
            .setTitle("Add Common Sites")
            .setMessage("Add ${availableSites.size} popular websites to blocked list?")
            .setPositiveButton("Add All") { _, _ ->
                availableSites.forEach { website ->
                    addWebsite(website)
                }
            }
            .setNegativeButton("Cancel", null)
            .show()
    }
}
{"name": "digital-wellbeing-backend", "version": "1.0.0", "description": "Backend for Digital Wellbeing application", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "migrate": "node migrations/run-migrations.js", "seed": "node scripts/seed.js"}, "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "mysql2": "^3.14.3", "sequelize": "^6.35.2", "socket.io": "^4.7.2", "winston": "^3.11.0"}, "devDependencies": {"axios": "^1.8.4", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}}
// Generated by view binder compiler. Do not edit!
package com.appblock.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ListView;
import android.widget.Spinner;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.appblock.R;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityWebsiteSelectionBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final TextInputEditText etSearch;

  @NonNull
  public final FloatingActionButton fabAddWebsite;

  @NonNull
  public final ListView lvWebsites;

  @NonNull
  public final Spinner spinnerCategories;

  private ActivityWebsiteSelectionBinding(@NonNull CoordinatorLayout rootView,
      @NonNull TextInputEditText etSearch, @NonNull FloatingActionButton fabAddWebsite,
      @NonNull ListView lvWebsites, @NonNull Spinner spinnerCategories) {
    this.rootView = rootView;
    this.etSearch = etSearch;
    this.fabAddWebsite = fabAddWebsite;
    this.lvWebsites = lvWebsites;
    this.spinnerCategories = spinnerCategories;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityWebsiteSelectionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityWebsiteSelectionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_website_selection, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityWebsiteSelectionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.et_search;
      TextInputEditText etSearch = ViewBindings.findChildViewById(rootView, id);
      if (etSearch == null) {
        break missingId;
      }

      id = R.id.fab_add_website;
      FloatingActionButton fabAddWebsite = ViewBindings.findChildViewById(rootView, id);
      if (fabAddWebsite == null) {
        break missingId;
      }

      id = R.id.lv_websites;
      ListView lvWebsites = ViewBindings.findChildViewById(rootView, id);
      if (lvWebsites == null) {
        break missingId;
      }

      id = R.id.spinner_categories;
      Spinner spinnerCategories = ViewBindings.findChildViewById(rootView, id);
      if (spinnerCategories == null) {
        break missingId;
      }

      return new ActivityWebsiteSelectionBinding((CoordinatorLayout) rootView, etSearch,
          fabAddWebsite, lvWebsites, spinnerCategories);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

package com.appblock

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.*
import com.appblock.data.BlockedAppsManager
import com.appblock.service.UsageStatsMonitorService
import com.appblock.test.CoreFunctionalityTest
import com.appblock.utils.PermissionUtils
import com.appblock.ui.WebsiteSelectionActivity
import com.appblock.service.WebBlockVpnService
import kotlinx.coroutines.runBlocking

/**
 * Simplified MainActivity for core functionality testing
 */
class MainActivity : Activity() {

    companion object {
        private const val TAG = "MainActivity"
    }

    // Store references to UI elements so we can update them
    private lateinit var statusText: TextView
    private lateinit var blockedCountText: TextView
    private lateinit var blockedWebsitesCountText: TextView
    private lateinit var vpnStatusText: TextView
    private lateinit var vpnSwitch: Switch

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        Log.d(TAG, "MainActivity created")

        // Create UI
        createUI()

        // Check if we need to show permission setup
        if (shouldShowPermissionSetup()) {
            showPermissionSetup()
        } else {
            // Run core functionality tests
            runCoreTests()

            // Start monitoring service if permissions are available
            startMonitoringIfReady()
        }

        val authManager = AuthManager.getInstance(this)
        if (!authManager.isAuthenticated()) {
            startActivity(Intent(this, RegistrationActivity::class.java))
            finish()
            return
        }

        // Initialize socket manager
        val socketManager = SocketManager.getInstance(this)
        socketManager.initialize()
        socketManager.connect()

        // Set up restriction update listener
        socketManager.setRestrictionUpdateListener(object : SocketManager.RestrictionUpdateListener {
            override fun onRestrictionUpdate(packageName: String, isBlocked: Boolean, timeLimit: Int?, startTime: String?, endTime: String?) {
                runOnUiThread {
                    val blockedAppsManager = BlockedAppsManager.getInstance(this@MainActivity)
                    if (isBlocked) {
                        blockedAppsManager.addBlockedApp(packageName)
                    } else {
                        blockedAppsManager.removeBlockedApp(packageName)
                    }
                    Log.d("MainActivity", "Updated restriction for $packageName: blocked=$isBlocked")
                }
            }
            
            override fun onBatchRestrictionUpdate(updates: List<SocketManager.RestrictionUpdate>) {
                runOnUiThread {
                    val blockedAppsManager = BlockedAppsManager.getInstance(this@MainActivity)
                    updates.forEach { update ->
                        if (update.isBlocked) {
                            blockedAppsManager.addBlockedApp(update.packageName)
                        } else {
                            blockedAppsManager.removeBlockedApp(update.packageName)
                        }
                    }
                    Log.d("MainActivity", "Processed batch update for ${updates.size} apps")
                }
            }
        })

        // Initialize blocked apps manager with database
        val blockedAppsManager = BlockedAppsManager.getInstance(this)

        // Update socket listener to use database
        socketManager.setRestrictionUpdateListener(object : SocketManager.RestrictionUpdateListener {
            override fun onRestrictionUpdate(packageName: String, isBlocked: Boolean, timeLimit: Int?, startTime: String?, endTime: String?) {
                lifecycleScope.launch {
                    blockedAppsManager.updateAppFromServer(packageName, isBlocked, timeLimit, startTime, endTime)
                    Log.d("MainActivity", "Updated restriction for $packageName: blocked=$isBlocked")
                }
            }
            
            override fun onBatchRestrictionUpdate(updates: List<SocketManager.RestrictionUpdate>) {
                lifecycleScope.launch {
                    updates.forEach { update ->
                        blockedAppsManager.updateAppFromServer(
                            update.packageName, 
                            update.isBlocked, 
                            update.timeLimit, 
                            update.startTime, 
                            update.endTime
                        )
                    }
                    Log.d("MainActivity", "Processed batch update for ${updates.size} apps")
                }
            }
        })

        // Observe blocked apps changes
        lifecycleScope.launch {
            blockedAppsManager.getBlockedAppsFlow().collect { blockedApps ->
                Log.d("MainActivity", "Blocked apps updated: ${blockedApps.size} apps")
                // Update UI or notify services as needed
            }
        }
    }

    private fun createUI() {
        val layout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(32, 32, 32, 32)
        }

        // Title
        val title = TextView(this).apply {
            text = "App Block - Parental Control"
            textSize = 24f
            setPadding(0, 0, 0, 32)
            gravity = android.view.Gravity.CENTER
        }
        layout.addView(title)

        // Select Apps Button
        val selectAppsButton = Button(this).apply {
            text = "Select Apps to Block"
            textSize = 18f
            setPadding(16, 16, 16, 16)
            setOnClickListener {
                openAppSelection()
            }
        }
        layout.addView(selectAppsButton)

        // Select Websites Button
        val selectWebsitesButton = Button(this).apply {
            text = "Select Websites to Block"
            textSize = 18f
            setPadding(16, 16, 16, 16)
            setOnClickListener {
                openWebsiteSelection()
            }
        }
        layout.addView(selectWebsitesButton)

        // VPN Blocking Toggle
        val vpnLayout = LinearLayout(this).apply {
            orientation = LinearLayout.HORIZONTAL
            setPadding(16, 16, 16, 16)
        }

        val vpnLabel = TextView(this).apply {
            text = "Network-Level Blocking: "
            textSize = 16f
            layoutParams = LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1f)
        }
        vpnLayout.addView(vpnLabel)

        vpnSwitch = Switch(this).apply {
            setOnCheckedChangeListener { _, isChecked ->
                toggleVpnBlocking(isChecked)
            }
        }
        vpnLayout.addView(vpnSwitch)
        layout.addView(vpnLayout)

        // Permission Setup Button
        val permissionButton = Button(this).apply {
            text = "Check Permissions"
            textSize = 16f
            setPadding(16, 16, 16, 16)
            setOnClickListener {
                openPermissionSetup()
            }
        }
        layout.addView(permissionButton)

        // Status Text
        statusText = TextView(this).apply {
            text = "Status: Checking..."
            textSize = 16f
            setPadding(0, 32, 0, 16)
        }
        layout.addView(statusText)

        // Blocked Apps Count
        blockedCountText = TextView(this).apply {
            text = "Blocked apps: 0"
            textSize = 14f
            setPadding(0, 0, 0, 16)
        }
        layout.addView(blockedCountText)

        setContentView(layout)
    }

    private fun shouldShowPermissionSetup(): Boolean {
        // Show permission setup if critical permissions are missing
        val hasCriticalPermissions = PermissionUtils.hasUsageStatsPermission(this) &&
                                   PermissionUtils.isAccessibilityServiceEnabled(this)

        Log.d(TAG, "Critical permissions check: $hasCriticalPermissions")
        return !hasCriticalPermissions
    }

    private fun showPermissionSetup() {
        Log.d(TAG, "Launching permission setup activity")

        // Update UI to show we're in setup mode
        statusText.text = "Status: ⚙️ Setup Required"
        blockedCountText.text = "Please complete setup first"

        val intent = Intent(this, com.appblock.ui.PermissionSetupActivity::class.java)
        startActivity(intent)
    }

    private fun openAppSelection() {
        Log.d(TAG, "Opening app selection...")
        val intent = Intent(this, com.appblock.ui.SimpleAppSelectionActivity::class.java)
        startActivity(intent)
    }

    private fun openPermissionSetup() {
        Log.d(TAG, "Opening permission setup...")
        val intent = Intent(this, com.appblock.ui.PermissionSetupActivity::class.java)
        startActivity(intent)
    }

    private fun openWebsiteSelection() {
        Log.d(TAG, "Opening website selection...")
        val intent = Intent(this, WebsiteSelectionActivity::class.java)
        startActivity(intent)
    }

    private fun toggleVpnBlocking(enabled: Boolean) {
        Log.d(TAG, "Toggling VPN blocking: $enabled")
        
        val blockedAppsManager = BlockedAppsManager.getInstance(this)
        
        if (enabled) {
            // Check VPN permission first
            if (!PermissionUtils.hasVpnPermission(this)) {
                vpnSwitch.isChecked = false
                checkVpnPermission()
                return
            }
            
            // Enable VPN blocking
            blockedAppsManager.setVpnBlockingEnabled(true)
            Toast.makeText(this, "Network-level blocking enabled", Toast.LENGTH_SHORT).show()
        } else {
            // Disable VPN blocking
            blockedAppsManager.setVpnBlockingEnabled(false)
            Toast.makeText(this, "Network-level blocking disabled", Toast.LENGTH_SHORT).show()
        }
        
        updateStatus()
    }

    private fun checkVpnPermission() {
        if (!PermissionUtils.hasVpnPermission(this)) {
            Log.d(TAG, "VPN permission not granted")
            
            val intent = PermissionUtils.requestVpnPermission(this)
            if (intent != null) {
                try {
                    startActivityForResult(intent, 100) // VPN permission request code
                } catch (e: Exception) {
                    Log.e(TAG, "Error requesting VPN permission", e)
                }
            }
        } else {
            Log.d(TAG, "VPN permission already granted")
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        
        when (requestCode) {
            100 -> { // VPN permission request
                if (resultCode == RESULT_OK) {
                    Log.d(TAG, "VPN permission granted")
                    Toast.makeText(this, "VPN permission granted", Toast.LENGTH_SHORT).show()
                    // User can now enable VPN blocking
                } else {
                    Log.d(TAG, "VPN permission denied")
                    Toast.makeText(this, "VPN permission is required for network-level website blocking", Toast.LENGTH_LONG).show()
                }
            }
        }
    }

    private fun runCoreTests() {
        Log.d(TAG, "Running core functionality tests...")

        try {
            val coreTest = CoreFunctionalityTest(this)
            coreTest.runBasicTests()

            Toast.makeText(this, "Core tests completed - check logs", Toast.LENGTH_LONG).show()
        } catch (e: Exception) {
            Log.e(TAG, "Error running core tests", e)
            Toast.makeText(this, "Core tests failed: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    private fun checkPermissions() {
        Log.d(TAG, "Checking permissions...")

        val hasUsageStats = PermissionUtils.hasUsageStatsPermission(this)
        val hasAccessibility = PermissionUtils.isAccessibilityServiceEnabled(this)
        val hasDeviceAdmin = PermissionUtils.isDeviceAdminActive(this)

        Log.d(TAG, "Usage Stats permission: $hasUsageStats")
        Log.d(TAG, "Accessibility Service: $hasAccessibility")
        Log.d(TAG, "Device Admin: $hasDeviceAdmin")

        if (!hasUsageStats) {
            Log.w(TAG, "Usage Stats permission not granted")
            Toast.makeText(this, "Please grant Usage Stats permission", Toast.LENGTH_LONG).show()
        }

        if (!hasAccessibility) {
            Log.w(TAG, "Accessibility Service not enabled")
            Toast.makeText(this, "Please enable Accessibility Service", Toast.LENGTH_LONG).show()
        }
    }

    private fun startMonitoringIfReady() {
        if (PermissionUtils.hasUsageStatsPermission(this)) {
            Log.d(TAG, "Starting Usage Stats monitoring service...")

            val serviceIntent = Intent(this, UsageStatsMonitorService::class.java)
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                startForegroundService(serviceIntent)
            } else {
                startService(serviceIntent)
            }

            Toast.makeText(this, "Monitoring service started", Toast.LENGTH_SHORT).show()
        } else {
            Log.w(TAG, "Cannot start monitoring - missing permissions")
        }
    }

    override fun onResume() {
        super.onResume()

        Log.d(TAG, "MainActivity resumed")

        // Check if we need to show permission setup again
        if (shouldShowPermissionSetup()) {
            showPermissionSetup()
        } else {
            // Update UI with current status
            updateStatus()

            // Start monitoring if not already running
            startMonitoringIfReady()
        }
    }

   private fun updateStatus() {
        Log.d(TAG, "Updating status...")

        val blockedAppsManager = BlockedAppsManager.getInstance(this)
        val blockedCount = blockedAppsManager.getBlockedApps().size
        val blockedWebsitesCount = blockedAppsManager.getBlockedWebsites().size

        // Check service status
        val usageStatsRunning = UsageStatsMonitorService.isServiceRunning()
        val accessibilityRunning = com.appblock.service.AppBlockAccessibilityService.isServiceRunning()
        val vpnRunning = WebBlockVpnService.isServiceRunning()
        val vpnEnabled = blockedAppsManager.isVpnBlockingEnabled()

        // Update VPN switch
        vpnSwitch.isChecked = vpnEnabled

        // Update status text
        val statusMessage = when {
            accessibilityRunning && usageStatsRunning && vpnRunning -> "Status: ✅ Full Protection (All services active)"
            accessibilityRunning && vpnRunning -> "Status: ✅ Active (Browser + Network blocking)"
            accessibilityRunning && usageStatsRunning -> "Status: ✅ Active (App + Browser blocking)"
            accessibilityRunning -> "Status: ✅ Partial (Browser blocking only)"
            vpnRunning -> "Status: ⚠️ Partial (Network blocking only)"
            usageStatsRunning -> "Status: ⚠️ Partial (Usage stats only)"
            else -> "Status: ❌ Inactive (No services running)"
        }

        statusText.text = statusMessage
        blockedCountText.text = "Blocked: $blockedCount apps, $blockedWebsitesCount websites"
    }

}

package com.appblock.database.entity

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "app_usage")
data class AppUsageEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val packageName: String,
    val appName: String,
    val usageTime: Long, // in milliseconds
    val date: String, // YYYY-MM-DD format
    val lastUsed: Long,
    val uploadedToServer: Boolean = false
)
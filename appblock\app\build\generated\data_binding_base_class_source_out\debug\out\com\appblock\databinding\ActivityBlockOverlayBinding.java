// Generated by view binder compiler. Do not edit!
package com.appblock.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.appblock.R;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityBlockOverlayBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final MaterialButton btnEnterPin;

  @NonNull
  public final MaterialButton btnGoHome;

  @NonNull
  public final MaterialButton btnRequestTime;

  @NonNull
  public final ImageView ivAppIcon;

  @NonNull
  public final TextView tvAppName;

  @NonNull
  public final TextView tvMessage;

  @NonNull
  public final TextView tvRemainingTime;

  @NonNull
  public final TextView tvSource;

  private ActivityBlockOverlayBinding(@NonNull FrameLayout rootView,
      @NonNull MaterialButton btnEnterPin, @NonNull MaterialButton btnGoHome,
      @NonNull MaterialButton btnRequestTime, @NonNull ImageView ivAppIcon,
      @NonNull TextView tvAppName, @NonNull TextView tvMessage, @NonNull TextView tvRemainingTime,
      @NonNull TextView tvSource) {
    this.rootView = rootView;
    this.btnEnterPin = btnEnterPin;
    this.btnGoHome = btnGoHome;
    this.btnRequestTime = btnRequestTime;
    this.ivAppIcon = ivAppIcon;
    this.tvAppName = tvAppName;
    this.tvMessage = tvMessage;
    this.tvRemainingTime = tvRemainingTime;
    this.tvSource = tvSource;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityBlockOverlayBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityBlockOverlayBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_block_overlay, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityBlockOverlayBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_enter_pin;
      MaterialButton btnEnterPin = ViewBindings.findChildViewById(rootView, id);
      if (btnEnterPin == null) {
        break missingId;
      }

      id = R.id.btn_go_home;
      MaterialButton btnGoHome = ViewBindings.findChildViewById(rootView, id);
      if (btnGoHome == null) {
        break missingId;
      }

      id = R.id.btn_request_time;
      MaterialButton btnRequestTime = ViewBindings.findChildViewById(rootView, id);
      if (btnRequestTime == null) {
        break missingId;
      }

      id = R.id.iv_app_icon;
      ImageView ivAppIcon = ViewBindings.findChildViewById(rootView, id);
      if (ivAppIcon == null) {
        break missingId;
      }

      id = R.id.tv_app_name;
      TextView tvAppName = ViewBindings.findChildViewById(rootView, id);
      if (tvAppName == null) {
        break missingId;
      }

      id = R.id.tv_message;
      TextView tvMessage = ViewBindings.findChildViewById(rootView, id);
      if (tvMessage == null) {
        break missingId;
      }

      id = R.id.tv_remaining_time;
      TextView tvRemainingTime = ViewBindings.findChildViewById(rootView, id);
      if (tvRemainingTime == null) {
        break missingId;
      }

      id = R.id.tv_source;
      TextView tvSource = ViewBindings.findChildViewById(rootView, id);
      if (tvSource == null) {
        break missingId;
      }

      return new ActivityBlockOverlayBinding((FrameLayout) rootView, btnEnterPin, btnGoHome,
          btnRequestTime, ivAppIcon, tvAppName, tvMessage, tvRemainingTime, tvSource);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

package com.appblock.ui

import android.app.Activity
import android.content.Intent
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.os.Bundle
import android.util.Log
import android.view.WindowManager
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import com.appblock.R
import com.appblock.data.BlockedAppsManager
import com.appblock.data.SettingsManager
import com.appblock.databinding.ActivityBlockOverlayBinding
import kotlinx.coroutines.runBlocking

class BlockOverlayActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "BlockOverlayActivity"
    }

    private lateinit var binding: ActivityBlockOverlayBinding
    private lateinit var settingsManager: SettingsManager
    private lateinit var blockedAppsManager: BlockedAppsManager
    
    private var blockedPackageName: String? = null
    private var source: String? = null

    private val pinAuthLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            // PIN verified, allow temporary access
            val duration = result.data?.getLongExtra("duration_ms", 0L) ?: 0L
            if (duration > 0 && blockedPackageName != null) {
                blockedAppsManager.temporarilyAllowApp(blockedPackageName!!, duration)
                Log.d(TAG, "Temporarily allowed $blockedPackageName for ${duration / 1000} seconds")
            }
            finish()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Make this activity fullscreen and secure
        window.setFlags(
            WindowManager.LayoutParams.FLAG_FULLSCREEN or
            WindowManager.LayoutParams.FLAG_SECURE or
            WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED,
            WindowManager.LayoutParams.FLAG_FULLSCREEN or
            WindowManager.LayoutParams.FLAG_SECURE or
            WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED
        )
        
        binding = ActivityBlockOverlayBinding.inflate(layoutInflater)
        setContentView(binding.root)

        settingsManager = SettingsManager(this)
        blockedAppsManager = BlockedAppsManager.getInstance(this)

        // Get blocked app info from intent
        blockedPackageName = intent.getStringExtra("blocked_package")
        source = intent.getStringExtra("source") ?: "unknown"

        if (blockedPackageName == null) {
            Log.e(TAG, "No blocked package specified")
            finish()
            return
        }

        setupUI()
        updateBlockedAppInfo()
    }

    private fun setupUI() {
        // Set up click listeners
        binding.btnEnterPin.setOnClickListener {
            launchPinAuthentication()
        }
        
        binding.btnGoHome.setOnClickListener {
            goToHome()
        }
        
        binding.btnRequestTime.setOnClickListener {
            requestMoreTime()
        }
        
        // Prevent back button from closing
        binding.root.isFocusableInTouchMode = true
        binding.root.requestFocus()
    }

    private fun updateBlockedAppInfo() {
        blockedPackageName?.let { packageName ->
            try {
                val packageManager = packageManager
                val appInfo: ApplicationInfo = packageManager.getApplicationInfo(packageName, 0)
                val appName = packageManager.getApplicationLabel(appInfo).toString()
                val appIcon = packageManager.getApplicationIcon(appInfo)
                
                binding.tvAppName.text = appName
                binding.ivAppIcon.setImageDrawable(appIcon)
                
                // Show remaining time if time limits are set
                val remainingTime = blockedAppsManager.getRemainingTime(packageName)
                if (remainingTime > 0) {
                    val minutes = remainingTime / (1000 * 60)
                    binding.tvRemainingTime.text = "Remaining time today: $minutes minutes"
                    binding.tvRemainingTime.visibility = android.view.View.VISIBLE
                    binding.btnRequestTime.visibility = android.view.View.VISIBLE
                } else {
                    binding.tvMessage.text = "This app is currently blocked"
                    binding.tvRemainingTime.visibility = android.view.View.GONE
                    binding.btnRequestTime.visibility = android.view.View.GONE
                }
                
            } catch (e: PackageManager.NameNotFoundException) {
                Log.e(TAG, "App not found: $packageName", e)
                binding.tvAppName.text = packageName
                binding.tvMessage.text = "This app is currently blocked"
            }
        }
        
        // Show source info for debugging
        binding.tvSource.text = "Detected by: $source"
    }

    private fun launchPinAuthentication() {
        val intent = Intent(this, PinAuthActivity::class.java).apply {
            putExtra("blocked_package", blockedPackageName)
            putExtra("action", "temporary_allow")
        }
        pinAuthLauncher.launch(intent)
    }

    private fun goToHome() {
        val homeIntent = Intent(Intent.ACTION_MAIN).apply {
            addCategory(Intent.CATEGORY_HOME)
            flags = Intent.FLAG_ACTIVITY_NEW_TASK
        }
        startActivity(homeIntent)
        finish()
    }

    private fun requestMoreTime() {
        val intent = Intent(this, PinAuthActivity::class.java).apply {
            putExtra("blocked_package", blockedPackageName)
            putExtra("action", "request_time")
        }
        pinAuthLauncher.launch(intent)
    }

    override fun onBackPressed() {
        // Prevent back button from closing the block screen
        // User must use the provided buttons
    }

    override fun onPause() {
        super.onPause()
        // If the activity is paused and the app is still blocked, 
        // we might want to go to home to prevent the blocked app from showing
        if (!isFinishing) {
            goToHome()
        }
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        // Handle new intents (e.g., if the same app is blocked again)
        setIntent(intent)
        
        val newBlockedPackage = intent?.getStringExtra("blocked_package")
        if (newBlockedPackage != null && newBlockedPackage != blockedPackageName) {
            blockedPackageName = newBlockedPackage
            source = intent.getStringExtra("source") ?: "unknown"
            updateBlockedAppInfo()
        }
    }
}

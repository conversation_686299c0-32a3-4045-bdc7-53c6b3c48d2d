<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="24dp"
    android:gravity="center">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="AppBlock Registration"
        android:textSize="24sp"
        android:textStyle="bold"
        android:layout_marginBottom="32dp" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Enter the registration code provided by your parent:"
        android:textSize="16sp"
        android:layout_marginBottom="16dp"
        android:textAlignment="center" />

    <EditText
        android:id="@+id/editTextCode"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Registration Code"
        android:inputType="textCapCharacters"
        android:layout_marginBottom="24dp"
        android:padding="16dp"
        android:background="@drawable/edit_text_background" />

    <Button
        android:id="@+id/buttonRegister"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Register Device"
        android:padding="16dp"
        android:textSize="16sp" />

</LinearLayout>
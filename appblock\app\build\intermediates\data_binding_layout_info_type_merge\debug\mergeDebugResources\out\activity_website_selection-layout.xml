<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_website_selection" modulePackage="com.appblock" filePath="app\src\main\res\layout\activity_website_selection.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_website_selection_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="93" endOffset="53"/></Target><Target id="@+id/et_search" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="33" startOffset="16" endLine="37" endOffset="63"/></Target><Target id="@+id/spinner_categories" view="Spinner"><Expressions/><location startLine="50" startOffset="12" endLine="54" endOffset="52"/></Target><Target id="@+id/lv_websites" view="ListView"><Expressions/><location startLine="65" startOffset="12" endLine="69" endOffset="52"/></Target><Target id="@+id/fab_add_website" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="85" startOffset="4" endLine="91" endOffset="42"/></Target></Targets></Layout>
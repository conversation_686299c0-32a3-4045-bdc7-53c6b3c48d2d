<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_block_overlay" modulePackage="com.appblock" filePath="app\src\main\res\layout\activity_block_overlay.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.FrameLayout"><Targets><Target tag="layout/activity_block_overlay_0" view="FrameLayout"><Expressions/><location startLine="1" startOffset="0" endLine="107" endOffset="13"/></Target><Target id="@+id/iv_app_icon" view="ImageView"><Expressions/><location startLine="19" startOffset="8" endLine="25" endOffset="46"/></Target><Target id="@+id/tv_app_name" view="TextView"><Expressions/><location startLine="27" startOffset="8" endLine="35" endOffset="38"/></Target><Target id="@+id/tv_message" view="TextView"><Expressions/><location startLine="38" startOffset="8" endLine="46" endOffset="37"/></Target><Target id="@+id/tv_remaining_time" view="TextView"><Expressions/><location startLine="49" startOffset="8" endLine="58" endOffset="39"/></Target><Target id="@+id/btn_enter_pin" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="66" startOffset="12" endLine="72" endOffset="46"/></Target><Target id="@+id/btn_request_time" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="74" startOffset="12" endLine="81" endOffset="53"/></Target><Target id="@+id/btn_go_home" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="83" startOffset="12" endLine="89" endOffset="46"/></Target><Target id="@+id/tv_source" view="TextView"><Expressions/><location startLine="94" startOffset="8" endLine="103" endOffset="39"/></Target></Targets></Layout>
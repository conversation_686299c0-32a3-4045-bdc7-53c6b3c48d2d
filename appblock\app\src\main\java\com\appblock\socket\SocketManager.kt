package com.appblock.socket

import android.content.Context
import android.util.Log
import com.appblock.api.ApiClient
import io.socket.client.IO
import io.socket.client.Socket
import io.socket.emitter.Emitter
import org.json.JSONObject
import java.net.URISyntaxException

class SocketManager private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "SocketManager"
        private const val SOCKET_URL = "http://10.219.117.104:3000"
        
        @Volatile
        private var INSTANCE: SocketManager? = null
        
        fun getInstance(context: Context): SocketManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: SocketManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private var socket: Socket? = null
    private var isConnected = false
    private var restrictionUpdateListener: RestrictionUpdateListener? = null
    
    interface RestrictionUpdateListener {
        fun onRestrictionUpdate(packageName: String, isBlocked: Boolean, timeLimit: Int?, startTime: String?, endTime: String?)
        fun onBatchRestrictionUpdate(updates: List<RestrictionUpdate>)
    }
    
    data class RestrictionUpdate(
        val packageName: String,
        val isBlocked: Boolean,
        val timeLimit: Int?,
        val startTime: String?,
        val endTime: String?
    )
    
    fun initialize() {
        try {
            val apiClient = ApiClient.getInstance(context)
            val token = apiClient.getAuthToken()
            
            if (token == null) {
                Log.e(TAG, "No auth token found, cannot initialize socket")
                return
            }
            
            val options = IO.Options().apply {
                forceNew = true
                reconnection = true
                reconnectionAttempts = 10
                reconnectionDelay = 5000
                timeout = 20000
            }
            
            socket = IO.socket(SOCKET_URL, options)
            
            // Add auth token to headers
            socket?.io()?.on(io.socket.engineio.client.EngineIOException.EVENT_TRANSPORT) { args ->
                val transport = args[0] as io.socket.engineio.client.Transport
                transport.on(io.socket.engineio.client.Transport.EVENT_REQUEST_HEADERS) { headerArgs ->
                    @Suppress("UNCHECKED_CAST")
                    val headers = headerArgs[0] as MutableMap<String, List<String>>
                    headers["Authorization"] = listOf("Bearer $token")
                }
            }
            
            setupEventListeners()
            Log.d(TAG, "Socket initialized successfully")
            
        } catch (e: URISyntaxException) {
            Log.e(TAG, "Invalid socket URL", e)
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing socket", e)
        }
    }
    
    private fun setupEventListeners() {
        socket?.apply {
            on(Socket.EVENT_CONNECT, onConnect)
            on(Socket.EVENT_DISCONNECT, onDisconnect)
            on(Socket.EVENT_CONNECT_ERROR, onConnectError)
            on("restriction:updated", onRestrictionUpdated)
            on("restriction:batch-updated", onBatchRestrictionUpdated)
        }
    }
    
    private val onConnect = Emitter.Listener {
        Log.d(TAG, "Socket connected")
        isConnected = true
    }
    
    private val onDisconnect = Emitter.Listener {
        Log.d(TAG, "Socket disconnected")
        isConnected = false
    }
    
    private val onConnectError = Emitter.Listener { args ->
        Log.e(TAG, "Socket connection error: ${args.contentToString()}")
        isConnected = false
    }
    
    private val onRestrictionUpdated = Emitter.Listener { args ->
        try {
            val data = args[0] as JSONObject
            val packageName = data.getString("packageName")
            val updates = data.getJSONObject("updates")
            
            val isBlocked = updates.getBoolean("blocked")
            val timeLimit = if (updates.has("timeLimit") && !updates.isNull("timeLimit")) {
                updates.getInt("timeLimit")
            } else null
            val startTime = if (updates.has("startTime") && !updates.isNull("startTime")) {
                updates.getString("startTime")
            } else null
            val endTime = if (updates.has("endTime") && !updates.isNull("endTime")) {
                updates.getString("endTime")
            } else null
            
            Log.d(TAG, "Received restriction update for $packageName: blocked=$isBlocked")
            restrictionUpdateListener?.onRestrictionUpdate(packageName, isBlocked, timeLimit, startTime, endTime)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error processing restriction update", e)
        }
    }
    
    private val onBatchRestrictionUpdated = Emitter.Listener { args ->
        try {
            val data = args[0] as JSONObject
            val updatesArray = data.getJSONArray("updates")
            val updates = mutableListOf<RestrictionUpdate>()
            
            for (i in 0 until updatesArray.length()) {
                val updateObj = updatesArray.getJSONObject(i)
                val packageName = updateObj.getString("packageName")
                val updatesData = updateObj.getJSONObject("updates")
                
                val isBlocked = updatesData.getBoolean("blocked")
                val timeLimit = if (updatesData.has("timeLimit") && !updatesData.isNull("timeLimit")) {
                    updatesData.getInt("timeLimit")
                } else null
                val startTime = if (updatesData.has("startTime") && !updatesData.isNull("startTime")) {
                    updatesData.getString("startTime")
                } else null
                val endTime = if (updatesData.has("endTime") && !updatesData.isNull("endTime")) {
                    updatesData.getString("endTime")
                } else null
                
                updates.add(RestrictionUpdate(packageName, isBlocked, timeLimit, startTime, endTime))
            }
            
            Log.d(TAG, "Received batch restriction update for ${updates.size} apps")
            restrictionUpdateListener?.onBatchRestrictionUpdate(updates)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error processing batch restriction update", e)
        }
    }
    
    fun connect() {
        socket?.connect()
    }
    
    fun disconnect() {
        socket?.disconnect()
    }
    
    fun isConnected(): Boolean = isConnected
    
    fun setRestrictionUpdateListener(listener: RestrictionUpdateListener) {
        this.restrictionUpdateListener = listener
    }
    
    fun cleanup() {
        socket?.disconnect()
        socket?.off()
        socket = null
        isConnected = false
    }
}
package com.appblock.api

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.appblock.model.*
import com.google.gson.Gson
import kotlinx.coroutines.runBlocking
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Response
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit

class ApiClient private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "ApiClient"
        private const val BASE_URL = "http://10.219.117.104:3000/api/"
        private const val PREF_NAME = "appblock_prefs"
        private const val PREF_AUTH_TOKEN = "auth_token"
        private const val PREF_REFRESH_TOKEN = "refresh_token"
        
        @Volatile
        private var INSTANCE: ApiClient? = null
        
        fun getInstance(context: Context): ApiClient {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ApiClient(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val prefs: SharedPreferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
    private val apiService: ApiService
    
    init {
        val loggingInterceptor = HttpLoggingInterceptor().apply {
            level = HttpLoggingInterceptor.Level.BODY
        }
        
        val authInterceptor = Interceptor { chain ->
            val originalRequest = chain.request()
            val token = getAuthToken()
            
            val newRequest = if (token != null) {
                originalRequest.newBuilder()
                    .addHeader("Authorization", "Bearer $token")
                    .build()
            } else {
                originalRequest
            }
            
            val response = chain.proceed(newRequest)
            
            // Handle token refresh if needed
            if (response.code == 401 && token != null) {
                response.close()
                val refreshed = refreshTokenSync()
                if (refreshed) {
                    val newToken = getAuthToken()
                    val retryRequest = originalRequest.newBuilder()
                        .addHeader("Authorization", "Bearer $newToken")
                        .build()
                    return@Interceptor chain.proceed(retryRequest)
                }
            }
            
            response
        }
        
        val client = OkHttpClient.Builder()
            .addInterceptor(loggingInterceptor)
            .addInterceptor(authInterceptor)
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .build()
        
        val retrofit = Retrofit.Builder()
            .baseUrl(BASE_URL)
            .client(client)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
        
        apiService = retrofit.create(ApiService::class.java)
    }
    
    // Token management
    fun saveTokens(authToken: String, refreshToken: String) {
        prefs.edit()
            .putString(PREF_AUTH_TOKEN, authToken)
            .putString(PREF_REFRESH_TOKEN, refreshToken)
            .apply()
    }
    
    fun getAuthToken(): String? = prefs.getString(PREF_AUTH_TOKEN, null)
    
    fun getRefreshToken(): String? = prefs.getString(PREF_REFRESH_TOKEN, null)
    
    fun clearTokens() {
        prefs.edit()
            .remove(PREF_AUTH_TOKEN)
            .remove(PREF_REFRESH_TOKEN)
            .apply()
    }
    
    fun isAuthenticated(): Boolean = getAuthToken() != null
    
    private fun refreshTokenSync(): Boolean {
        return try {
            val refreshToken = getRefreshToken() ?: return false
            val response = runBlocking {
                apiService.refreshToken(RefreshTokenRequest(refreshToken))
            }
            
            if (response.isSuccessful && response.body()?.success == true) {
                val authResponse = response.body()?.data
                if (authResponse != null) {
                    saveTokens(authResponse.token, authResponse.refreshToken)
                    true
                } else false
            } else {
                clearTokens()
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Token refresh failed", e)
            clearTokens()
            false
        }
    }
    
    // API methods
    suspend fun useRegistrationCode(code: String, request: RegistrationRequest): Response<ApiResponse<AuthResponse>> {
        return apiService.useRegistrationCode(code, request)
    }
    
    suspend fun uploadAppUsage(appUsageDataList: List<AppUsageData>): Response<ApiResponse<Void>> {
        return apiService.uploadAppUsage(appUsageDataList)
    }
    
    suspend fun getBlockedApps(): Response<ApiResponse<List<BlockedApp>>> {
        return apiService.getBlockedApps()
    }
    
    suspend fun getCurrentUser(): Response<ApiResponse<User>> {
        return apiService.getCurrentUser()
    }
}
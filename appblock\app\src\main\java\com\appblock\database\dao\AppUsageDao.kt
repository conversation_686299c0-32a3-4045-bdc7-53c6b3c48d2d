package com.appblock.database.dao

import androidx.room.*
import com.appblock.database.entity.AppUsageEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface AppUsageDao {
    
    @Query("SELECT * FROM app_usage WHERE uploadedToServer = 0 ORDER BY lastUsed DESC")
    suspend fun getUnuploadedUsage(): List<AppUsageEntity>
    
    @Query("SELECT * FROM app_usage WHERE date = :date ORDER BY usageTime DESC")
    fun getUsageForDate(date: String): Flow<List<AppUsageEntity>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUsage(usage: AppUsageEntity)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUsageList(usageList: List<AppUsageEntity>)
    
    @Update
    suspend fun updateUsage(usage: AppUsageEntity)
    
    @Query("UPDATE app_usage SET uploadedToServer = 1 WHERE id IN (:ids)")
    suspend fun markAsUploaded(ids: List<Long>)
    
    @Query("DELETE FROM app_usage WHERE date < :cutoffDate")
    suspend fun deleteOldUsage(cutoffDate: String)
}
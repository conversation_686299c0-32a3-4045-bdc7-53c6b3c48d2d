{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T17:13:18.649Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T17:13:18.949Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-12T17:13:18.954Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T17:36:12.227Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T17:36:12.950Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-12T17:36:12.972Z"}
{"level":"error","message":"AppError: Validation error","method":"POST","path":"/api/auth/login","service":"digital-wellbeing-api","stack":"AppError: Validation error\n    at validate (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\validation.js:13:17)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-12T17:37:24.209Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T17:39:18.630Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T17:39:19.180Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-12T17:39:19.185Z"}
{"level":"info","message":"Starting database migration...","service":"digital-wellbeing-api","timestamp":"2025-04-12T17:40:37.483Z"}
{"level":"info","message":"Database tables created successfully","service":"digital-wellbeing-api","timestamp":"2025-04-12T17:40:38.151Z"}
{"level":"info","message":"Starting database seeding...","service":"digital-wellbeing-api","timestamp":"2025-04-12T17:41:01.723Z"}
{"level":"info","message":"Database seeding completed successfully","service":"digital-wellbeing-api","timestamp":"2025-04-12T17:41:03.328Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T17:50:34.004Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T17:50:34.496Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-12T17:50:34.501Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T17:54:01.473Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T17:54:02.020Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-12T17:54:02.024Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T17:54:29.628Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T17:54:30.337Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-12T17:54:30.342Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T17:54:38.420Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T17:54:38.998Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-12T17:54:39.003Z"}
{"level":"error","message":"AppError: Invalid credentials","method":"POST","path":"/api/auth/login","service":"digital-wellbeing-api","stack":"AppError: Invalid credentials\n    at exports.login (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\authController.js:90:19)","timestamp":"2025-04-12T17:54:45.176Z"}
{"level":"error","message":"AppError: Invalid credentials","method":"POST","path":"/api/auth/login","service":"digital-wellbeing-api","stack":"AppError: Invalid credentials\n    at exports.login (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\authController.js:90:19)","timestamp":"2025-04-12T17:55:17.518Z"}
{"level":"error","message":"AppError: Invalid credentials","method":"POST","path":"/api/auth/login","service":"digital-wellbeing-api","stack":"AppError: Invalid credentials\n    at exports.login (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\authController.js:90:19)","timestamp":"2025-04-12T17:56:07.816Z"}
{"level":"error","message":"AppError: Validation error","method":"POST","path":"/api/auth/register","service":"digital-wellbeing-api","stack":"AppError: Validation error\n    at validate (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\validation.js:13:17)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-12T17:57:43.329Z"}
{"level":"error","message":"AppError: Validation error","method":"POST","path":"/api/auth/register","service":"digital-wellbeing-api","stack":"AppError: Validation error\n    at validate (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\validation.js:13:17)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-12T17:57:56.463Z"}
{"level":"error","message":"AppError: Validation error","method":"POST","path":"/api/auth/register","service":"digital-wellbeing-api","stack":"AppError: Validation error\n    at validate (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\validation.js:13:17)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-12T17:58:14.583Z"}
{"level":"error","message":"AppError: Invalid credentials","method":"POST","path":"/api/auth/login","service":"digital-wellbeing-api","stack":"AppError: Invalid credentials\n    at exports.login (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\authController.js:84:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-12T18:03:34.055Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T18:05:06.722Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T18:05:07.358Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-12T18:05:07.365Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T18:05:25.950Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T18:05:26.601Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-12T18:05:26.643Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T18:06:44.295Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T18:06:44.924Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-12T18:06:44.930Z"}
{"level":"error","message":"AppError: Invalid credentials","method":"POST","path":"/api/auth/login","service":"digital-wellbeing-api","stack":"AppError: Invalid credentials\n    at exports.login (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\authController.js:84:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-12T18:08:59.196Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T18:51:02.303Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T18:51:02.767Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-12T18:51:02.772Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T18:57:21.385Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T18:57:21.893Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-12T18:57:21.897Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T19:05:41.828Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T19:05:42.364Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-12T19:05:42.368Z"}
{"level":"error","message":"AppError: Not authorized to access this route","method":"GET","path":"/api/auth/me","service":"digital-wellbeing-api","stack":"AppError: Not authorized to access this route\n    at protect (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\auth.js:51:19)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-04-12T19:07:47.327Z"}
{"level":"error","message":"AppError: Not authorized to access this route","method":"GET","path":"/api/relationships/parent","service":"digital-wellbeing-api","stack":"AppError: Not authorized to access this route\n    at protect (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\auth.js:51:19)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)","timestamp":"2025-04-12T19:07:47.335Z"}
{"level":"error","message":"AppError: Not authorized to access this route","method":"GET","path":"/api/relationships/parent","service":"digital-wellbeing-api","stack":"AppError: Not authorized to access this route\n    at protect (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\auth.js:51:19)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)","timestamp":"2025-04-12T19:08:06.065Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T19:11:05.647Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T19:11:06.335Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-12T19:11:06.371Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T19:11:23.467Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T19:11:24.187Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-12T19:11:24.311Z"}
{"level":"info","message":"Starting database migration...","service":"digital-wellbeing-api","timestamp":"2025-04-12T19:26:10.662Z"}
{"level":"info","message":"Created tables:","service":"digital-wellbeing-api","timestamp":"2025-04-12T19:26:11.062Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T19:26:35.201Z"}
{"fields":null,"index":"parent_child_relationships_parent_id_fkey","level":"error","message":"Unable to connect to the database: insert or update on table \"parent_child_relationships\" violates foreign key constraint \"parent_child_relationships_parent_id_fkey\"","name":"SequelizeForeignKeyConstraintError","original":{"code":"23503","constraint":"parent_child_relationships_parent_id_fkey","detail":"Key (parent_id)=(1) is not present in table \"users\".","file":"ri_triggers.c","length":332,"line":"2610","name":"error","routine":"ri_ReportViolation","schema":"public","severity":"ERROR","sql":"ALTER TABLE \"parent_child_relationships\" ALTER COLUMN \"parent_id\" SET NOT NULL;ALTER TABLE \"parent_child_relationships\"  ADD FOREIGN KEY (\"parent_id\") REFERENCES \"users\" (\"id\") ON DELETE NO ACTION ON UPDATE CASCADE;","table":"parent_child_relationships"},"parameters":{},"parent":{"code":"23503","constraint":"parent_child_relationships_parent_id_fkey","detail":"Key (parent_id)=(1) is not present in table \"users\".","file":"ri_triggers.c","length":332,"line":"2610","name":"error","routine":"ri_ReportViolation","schema":"public","severity":"ERROR","sql":"ALTER TABLE \"parent_child_relationships\" ALTER COLUMN \"parent_id\" SET NOT NULL;ALTER TABLE \"parent_child_relationships\"  ADD FOREIGN KEY (\"parent_id\") REFERENCES \"users\" (\"id\") ON DELETE NO ACTION ON UPDATE CASCADE;","table":"parent_child_relationships"},"service":"digital-wellbeing-api","sql":"ALTER TABLE \"parent_child_relationships\" ALTER COLUMN \"parent_id\" SET NOT NULL;ALTER TABLE \"parent_child_relationships\"  ADD FOREIGN KEY (\"parent_id\") REFERENCES \"users\" (\"id\") ON DELETE NO ACTION ON UPDATE CASCADE;","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Relationship.sync (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\model.js:984:11)\n    at async Sequelize.sync (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\sequelize.js:377:9)\n    at async startServer (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\server.js:23:5)","table":"parent_child_relationships","timestamp":"2025-04-12T19:26:35.458Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T19:29:07.197Z"}
{"fields":null,"index":"parent_child_relationships_parent_id_fkey","level":"error","message":"Unable to connect to the database: insert or update on table \"parent_child_relationships\" violates foreign key constraint \"parent_child_relationships_parent_id_fkey\"","name":"SequelizeForeignKeyConstraintError","original":{"code":"23503","constraint":"parent_child_relationships_parent_id_fkey","detail":"Key (parent_id)=(1) is not present in table \"users\".","file":"ri_triggers.c","length":332,"line":"2610","name":"error","routine":"ri_ReportViolation","schema":"public","severity":"ERROR","sql":"ALTER TABLE \"parent_child_relationships\" ALTER COLUMN \"parent_id\" SET NOT NULL;ALTER TABLE \"parent_child_relationships\"  ADD FOREIGN KEY (\"parent_id\") REFERENCES \"users\" (\"id\") ON DELETE NO ACTION ON UPDATE CASCADE;","table":"parent_child_relationships"},"parameters":{},"parent":{"code":"23503","constraint":"parent_child_relationships_parent_id_fkey","detail":"Key (parent_id)=(1) is not present in table \"users\".","file":"ri_triggers.c","length":332,"line":"2610","name":"error","routine":"ri_ReportViolation","schema":"public","severity":"ERROR","sql":"ALTER TABLE \"parent_child_relationships\" ALTER COLUMN \"parent_id\" SET NOT NULL;ALTER TABLE \"parent_child_relationships\"  ADD FOREIGN KEY (\"parent_id\") REFERENCES \"users\" (\"id\") ON DELETE NO ACTION ON UPDATE CASCADE;","table":"parent_child_relationships"},"service":"digital-wellbeing-api","sql":"ALTER TABLE \"parent_child_relationships\" ALTER COLUMN \"parent_id\" SET NOT NULL;ALTER TABLE \"parent_child_relationships\"  ADD FOREIGN KEY (\"parent_id\") REFERENCES \"users\" (\"id\") ON DELETE NO ACTION ON UPDATE CASCADE;","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Relationship.sync (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\model.js:984:11)\n    at async Sequelize.sync (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\sequelize.js:377:9)\n    at async startServer (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\server.js:23:5)","table":"parent_child_relationships","timestamp":"2025-04-12T19:29:07.469Z"}
{"level":"info","message":"Starting database migration...","service":"digital-wellbeing-api","timestamp":"2025-04-12T19:32:43.124Z"}
{"level":"info","message":"Dropped all existing tables","service":"digital-wellbeing-api","timestamp":"2025-04-12T19:32:43.433Z"}
{"level":"info","message":"Created Users table","service":"digital-wellbeing-api","timestamp":"2025-04-12T19:32:43.466Z"}
{"level":"info","message":"Created AuthTokens table","service":"digital-wellbeing-api","timestamp":"2025-04-12T19:32:43.488Z"}
{"level":"info","message":"Created Relationships table","service":"digital-wellbeing-api","timestamp":"2025-04-12T19:32:43.507Z"}
{"level":"info","message":"Created tables:","service":"digital-wellbeing-api","timestamp":"2025-04-12T19:32:43.514Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T19:32:55.079Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T19:32:55.575Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-12T19:32:55.670Z"}
{"level":"error","message":"AppError: Not authorized to access this route","method":"GET","path":"/api/auth/me","service":"digital-wellbeing-api","stack":"AppError: Not authorized to access this route\n    at protect (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\auth.js:51:19)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-04-12T19:36:55.601Z"}
{"level":"error","message":"AppError: Not authorized to access this route","method":"GET","path":"/api/relationships/parent","service":"digital-wellbeing-api","stack":"AppError: Not authorized to access this route\n    at protect (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\auth.js:51:19)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)","timestamp":"2025-04-12T19:36:55.615Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T19:48:44.949Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T19:48:45.455Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-12T19:48:45.460Z"}
{"level":"error","message":"AppError: Not authorized to access this route","method":"GET","path":"/api/relationships/parent","service":"digital-wellbeing-api","stack":"AppError: Not authorized to access this route\n    at protect (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\auth.js:51:19)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)","timestamp":"2025-04-12T19:50:16.640Z"}
{"level":"info","message":"Starting database migration...","service":"digital-wellbeing-api","timestamp":"2025-04-12T19:54:12.635Z"}
{"level":"info","message":"Dropped all existing tables","service":"digital-wellbeing-api","timestamp":"2025-04-12T19:54:13.161Z"}
{"level":"info","message":"Created Users table","service":"digital-wellbeing-api","timestamp":"2025-04-12T19:54:13.196Z"}
{"level":"info","message":"Created AuthTokens table","service":"digital-wellbeing-api","timestamp":"2025-04-12T19:54:13.218Z"}
{"level":"info","message":"Created Relationships table","service":"digital-wellbeing-api","timestamp":"2025-04-12T19:54:13.236Z"}
{"level":"info","message":"Created tables:","service":"digital-wellbeing-api","timestamp":"2025-04-12T19:54:13.241Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T19:54:24.236Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T19:54:24.793Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-12T19:54:24.799Z"}
{"level":"error","message":"AppError: Not authorized to access this route","method":"GET","path":"/api/relationships/parent","service":"digital-wellbeing-api","stack":"AppError: Not authorized to access this route\n    at protect (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\auth.js:51:19)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)","timestamp":"2025-04-12T19:56:19.416Z"}
{"level":"error","message":"AppError: Not authorized to access this route","method":"GET","path":"/api/relationships/parent","service":"digital-wellbeing-api","stack":"AppError: Not authorized to access this route\n    at protect (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\auth.js:51:19)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)","timestamp":"2025-04-12T19:58:17.029Z"}
{"level":"error","message":"AppError: Not authorized to access this route","method":"GET","path":"/api/relationships/parent","service":"digital-wellbeing-api","stack":"AppError: Not authorized to access this route\n    at protect (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\auth.js:51:19)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)","timestamp":"2025-04-12T19:58:22.391Z"}
{"level":"error","message":"AppError: Not authorized to access this route","method":"GET","path":"/api/relationships/parent","service":"digital-wellbeing-api","stack":"AppError: Not authorized to access this route\n    at protect (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\auth.js:51:19)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)","timestamp":"2025-04-12T19:58:23.571Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T20:10:07.748Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T20:10:08.319Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-12T20:10:08.323Z"}
{"level":"error","message":"AppError: Not authorized to access this route","method":"GET","path":"/api/relationships/parent","service":"digital-wellbeing-api","stack":"AppError: Not authorized to access this route\n    at protect (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\auth.js:51:19)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)","timestamp":"2025-04-12T20:12:40.458Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T20:14:09.264Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T20:14:09.971Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-12T20:14:10.062Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T20:22:54.813Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T20:22:55.591Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-12T20:22:55.597Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T20:39:46.295Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T20:39:47.245Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-12T20:39:47.252Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T20:40:45.744Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-12T20:40:47.367Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-12T20:40:47.376Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-12T20:42:40.094Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T06:18:50.542Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T06:18:51.264Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-13T06:18:51.284Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T06:19:17.057Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T06:19:17.706Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-13T06:19:17.725Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T06:21:27.431Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T06:21:28.026Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-13T06:21:28.031Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T06:46:45.105Z"}
{"level":"error","message":"AppError: Not authorized to access this route","method":"POST","path":"/api/relationships","service":"digital-wellbeing-api","stack":"AppError: Not authorized to access this route\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\auth.js:65:19\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at protect (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\auth.js:50:7)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-13T06:47:37.015Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T07:00:53.968Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T07:08:40.054Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T07:08:40.665Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-13T07:08:40.670Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T07:08:49.346Z"}
{"level":"error","message":"AppError: Not authorized to access this route","method":"POST","path":"/api/relationships","service":"digital-wellbeing-api","stack":"AppError: Not authorized to access this route\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\auth.js:83:19\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at protect (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\auth.js:57:7)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-13T07:09:15.799Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T07:12:38.604Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T07:14:14.727Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T07:14:15.489Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-13T07:14:15.495Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T07:14:57.518Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T07:14:58.371Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-13T07:14:58.376Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T07:17:37.350Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T07:17:37.891Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-13T07:17:37.898Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T07:19:32.631Z"}
{"level":"error","message":"AppError: Not authorized to access this route","method":"POST","path":"/api/relationships","service":"digital-wellbeing-api","stack":"AppError: Not authorized to access this route\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\auth.js:90:19\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at protect (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\auth.js:57:7)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-13T07:19:54.579Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T07:33:49.405Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T07:33:49.909Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-13T07:33:49.914Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T07:35:11.408Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T08:03:41.293Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T08:12:06.557Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T08:12:07.094Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-13T08:12:07.098Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T08:14:16.876Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T08:14:58.447Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T08:22:25.430Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T08:22:25.959Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-13T08:22:25.963Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T08:24:21.841Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T08:24:44.489Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T08:32:23.343Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T08:33:16.473Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T09:01:30.200Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T09:05:48.720Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T09:07:59.376Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T09:12:02.011Z"}
{"level":"error","message":"SequelizeDatabaseError: invalid input syntax for type integer: \"block\"","method":"PUT","path":"/api/child-apps/block","service":"digital-wellbeing-api","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.select (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:407:12)\n    at async ChildApp.findAll (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\model.js:1140:21)\n    at async ChildApp.findOne (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\model.js:1240:12)\n    at async ChildApp.findByPk (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\model.js:1221:12)\n    at async exports.updateChildApp (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\childAppController.js:140:22)","timestamp":"2025-04-13T09:12:28.024Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T09:16:14.499Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T09:16:17.328Z"}
{"level":"error","message":"SequelizeDatabaseError: invalid input syntax for type time: \"540\"","method":"PUT","path":"/api/child-apps/14","service":"digital-wellbeing-api","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.update (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:355:12)\n    at async model.save (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async exports.updateChildApp (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\childAppController.js:179:5)","timestamp":"2025-04-13T09:19:03.998Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T09:21:12.528Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T09:21:19.044Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T09:21:19.051Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T09:21:19.062Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T09:21:19.064Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T09:21:19.065Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T09:21:29.925Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T09:24:18.250Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T09:24:19.926Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T09:26:24.454Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T09:28:58.374Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T09:32:10.150Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T09:32:10.152Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T09:32:10.154Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T09:32:10.157Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T09:32:10.159Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T09:32:27.164Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T09:53:59.458Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T09:54:20.803Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:01:34.049Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:01:54.546Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:01:58.773Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:04:17.964Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:10:30.733Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:10:30.737Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:10:30.738Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:10:48.537Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:10:51.692Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:15:37.701Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:17:01.996Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:17:27.021Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:18:18.186Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:19:50.544Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:20:35.330Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:21:16.136Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:23:56.222Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:25:37.502Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:28:22.907Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:37:07.224Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:37:07.227Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:37:07.228Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:37:07.229Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:37:07.230Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:37:07.320Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:37:07.367Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:37:07.397Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:37:07.428Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:37:07.472Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:37:07.510Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:37:07.543Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:42:41.108Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:43:20.645Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:43:28.227Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:51:07.008Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:51:07.544Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:51:07.549Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:51:09.549Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:51:18.384Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:51:19.043Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:51:19.049Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:51:20.404Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:51:39.789Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:51:40.434Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:51:40.440Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:51:40.813Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:52:06.931Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:52:07.597Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:52:07.603Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:52:09.184Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:52:29.384Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:52:30.070Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:52:30.077Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:52:31.829Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:52:45.588Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:52:46.603Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:52:46.609Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:52:46.947Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:53:06.828Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:53:07.503Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:53:07.509Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:53:09.501Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:53:26.812Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:53:27.631Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:53:27.636Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:53:29.284Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:53:44.229Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:53:45.092Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:53:45.099Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:53:46.782Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:53:54.395Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:53:55.307Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:53:55.311Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T10:53:56.866Z"}
{"level":"error","message":"Failed to create migrations table: password authentication failed for user \"postgres\"","service":"digital-wellbeing-api","timestamp":"2025-04-13T11:12:10.889Z"}
{"level":"info","message":"Running migration: 20240501_initial_migration","service":"digital-wellbeing-api","timestamp":"2025-04-13T11:15:59.313Z"}
{"level":"info","message":"Starting initial migration","service":"digital-wellbeing-api","timestamp":"2025-04-13T11:15:59.315Z"}
{"level":"info","message":"Initial migration completed successfully","service":"digital-wellbeing-api","timestamp":"2025-04-13T11:15:59.333Z"}
{"level":"info","message":"Migration 20240501_initial_migration completed successfully","service":"digital-wellbeing-api","timestamp":"2025-04-13T11:15:59.337Z"}
{"level":"info","message":"Running migration: 20240601_add_guardian_tables","service":"digital-wellbeing-api","timestamp":"2025-04-13T11:15:59.339Z"}
{"level":"info","message":"Starting migration: Adding guardian tables","service":"digital-wellbeing-api","timestamp":"2025-04-13T11:15:59.340Z"}
{"level":"info","message":"Migration completed successfully","service":"digital-wellbeing-api","timestamp":"2025-04-13T11:15:59.380Z"}
{"level":"info","message":"Migration 20240601_add_guardian_tables completed successfully","service":"digital-wellbeing-api","timestamp":"2025-04-13T11:15:59.383Z"}
{"level":"info","message":"All migrations completed successfully","service":"digital-wellbeing-api","timestamp":"2025-04-13T11:15:59.384Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T11:19:56.334Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T11:19:57.049Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-13T11:19:57.054Z"}
{"level":"info","message":"User connected: 9","service":"digital-wellbeing-api","timestamp":"2025-04-13T11:21:42.468Z"}
{"level":"info","message":"User disconnected: 9","service":"digital-wellbeing-api","timestamp":"2025-04-13T11:23:39.744Z"}
{"level":"info","message":"User connected: 10","service":"digital-wellbeing-api","timestamp":"2025-04-13T11:29:00.590Z"}
{"level":"info","message":"User disconnected: 10","service":"digital-wellbeing-api","timestamp":"2025-04-13T11:39:32.158Z"}
{"level":"info","message":"User connected: 10","service":"digital-wellbeing-api","timestamp":"2025-04-13T11:39:51.194Z"}
{"level":"info","message":"User connected: 10","service":"digital-wellbeing-api","timestamp":"2025-04-13T11:39:54.610Z"}
{"level":"info","message":"User disconnected: 10","service":"digital-wellbeing-api","timestamp":"2025-04-13T11:48:26.801Z"}
{"level":"info","message":"User disconnected: 10","service":"digital-wellbeing-api","timestamp":"2025-04-13T11:48:26.806Z"}
{"level":"info","message":"User connected: 10","service":"digital-wellbeing-api","timestamp":"2025-04-13T11:48:44.150Z"}
{"level":"info","message":"User connected: 10","service":"digital-wellbeing-api","timestamp":"2025-04-13T11:49:59.681Z"}
{"level":"info","message":"User disconnected: 10","service":"digital-wellbeing-api","timestamp":"2025-04-13T11:50:02.017Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T11:51:12.224Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T11:52:39.534Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T11:52:40.543Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-13T11:52:40.548Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T11:52:41.297Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T11:53:01.575Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T11:53:02.539Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-13T11:53:02.629Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T11:53:03.753Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T11:55:01.573Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T11:56:13.326Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T11:56:14.155Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-13T11:56:14.162Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T11:59:16.246Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T12:24:21.467Z"}
{"level":"info","message":"Sharing 0 children with secondary guardian 11","service":"digital-wellbeing-api","timestamp":"2025-04-13T12:33:30.751Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T12:34:58.149Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T12:34:58.902Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-13T12:34:58.907Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T12:35:11.690Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T12:35:12.432Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-13T12:35:12.436Z"}
{"level":"error","message":"AppError: Not authorized to access this route","method":"GET","path":"/api/relationships/parent","service":"digital-wellbeing-api","stack":"AppError: Not authorized to access this route\n    at protect (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\middleware\\auth.js:21:19)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)","timestamp":"2025-04-13T12:39:10.847Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T12:44:34.348Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T12:44:35.229Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-13T12:44:35.234Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T12:46:31.741Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T12:53:55.139Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T12:53:56.286Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-13T12:53:56.307Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T12:53:57.749Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T12:54:23.453Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T12:54:24.437Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T12:55:00.684Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T12:55:57.659Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T12:55:59.928Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-13T12:55:59.994Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T12:56:03.301Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T12:57:19.634Z"}
{"level":"error","message":"SequelizeDatabaseError: relation \"parent_child_relationships\" does not exist","method":"GET","path":"/api/relationships/parent","service":"digital-wellbeing-api","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.select (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:407:12)\n    at async Relationship.findAll (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\model.js:1140:21)\n    at async exports.getParentRelationships (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\relationshipController.js:10:27)","timestamp":"2025-04-13T12:58:38.443Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T12:58:38.563Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T12:59:39.571Z"}
{"level":"error","message":"Unable to connect to the database: relation \"relationships_parent_id_child_id\" already exists","name":"SequelizeDatabaseError","original":{"code":"42P07","file":"index.c","length":114,"line":"900","name":"error","routine":"index_create","severity":"ERROR","sql":"CREATE UNIQUE INDEX \"relationships_parent_id_child_id\" ON \"relationships\" (\"parent_id\", \"child_id\")"},"parameters":{},"parent":{"code":"42P07","file":"index.c","length":114,"line":"900","name":"error","routine":"index_create","severity":"ERROR","sql":"CREATE UNIQUE INDEX \"relationships_parent_id_child_id\" ON \"relationships\" (\"parent_id\", \"child_id\")"},"service":"digital-wellbeing-api","sql":"CREATE UNIQUE INDEX \"relationships_parent_id_child_id\" ON \"relationships\" (\"parent_id\", \"child_id\")","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.addIndex (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:250:12)\n    at async Relationship.sync (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\model.js:999:7)\n    at async Sequelize.sync (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\sequelize.js:377:9)\n    at async startServer (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\server.js:23:5)","timestamp":"2025-04-13T12:59:40.189Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:00:16.397Z"}
{"level":"error","message":"Unable to connect to the database: relation \"relationships_parent_id_child_id\" already exists","name":"SequelizeDatabaseError","original":{"code":"42P07","file":"index.c","length":114,"line":"900","name":"error","routine":"index_create","severity":"ERROR","sql":"CREATE UNIQUE INDEX \"relationships_parent_id_child_id\" ON \"relationships\" (\"parent_id\", \"child_id\")"},"parameters":{},"parent":{"code":"42P07","file":"index.c","length":114,"line":"900","name":"error","routine":"index_create","severity":"ERROR","sql":"CREATE UNIQUE INDEX \"relationships_parent_id_child_id\" ON \"relationships\" (\"parent_id\", \"child_id\")"},"service":"digital-wellbeing-api","sql":"CREATE UNIQUE INDEX \"relationships_parent_id_child_id\" ON \"relationships\" (\"parent_id\", \"child_id\")","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async PostgresQueryInterface.addIndex (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:250:12)\n    at async Relationship.sync (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\model.js:999:7)\n    at async Sequelize.sync (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\node_modules\\sequelize\\lib\\sequelize.js:377:9)\n    at async startServer (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\server.js:23:5)","timestamp":"2025-04-13T13:00:16.952Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:02:07.901Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:02:07.987Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:02:07.991Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:03:26.297Z"}
{"level":"info","message":"User connected: 11","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:05:04.786Z"}
{"level":"info","message":"User disconnected: 11","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:05:56.714Z"}
{"level":"error","message":"AppError: Referral code has already been used","method":"POST","path":"/api/auth/register","service":"digital-wellbeing-api","stack":"AppError: Referral code has already been used\n    at exports.register (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\authController.js:63:21)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-13T13:06:38.810Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:10:56.320Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:10:56.404Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:10:56.408Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:10:56.919Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:10:59.602Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:11:15.054Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:14:54.355Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:14:54.457Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:14:54.462Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:14:55.173Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:14:57.658Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:15:06.498Z"}
{"level":"info","message":"Sharing 4 children with secondary guardian 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:16:32.137Z"}
{"level":"info","message":"User connected: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:16:37.351Z"}
{"level":"info","message":"User disconnected: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:21:50.411Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:32:59.362Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:32:59.458Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:32:59.462Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:33:00.132Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:33:07.947Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:33:16.649Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:33:32.864Z"}
{"level":"info","message":"User connected: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:34:25.309Z"}
{"level":"info","message":"User disconnected: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:48:00.000Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:50:26.227Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:50:26.335Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:50:26.341Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:51:25.509Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:51:25.635Z"}
{"level":"info","message":"Getting accessible children for secondary guardian ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:52:33.637Z"}
{"level":"info","message":"User connected: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:52:33.652Z"}
{"level":"info","message":"Found 1 guardian relationships","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:52:33.665Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:52:33.683Z"}
{"level":"info","message":"Found 4 parent-child relationships","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:52:33.718Z"}
{"level":"info","message":"Relationship 1: parent_id=1, child_id=2, child=Child1","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:52:33.732Z"}
{"level":"info","message":"Relationship 2: parent_id=1, child_id=3, child=Child2","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:52:33.735Z"}
{"level":"info","message":"Relationship 3: parent_id=1, child_id=4, child=Child3","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:52:33.736Z"}
{"level":"info","message":"Relationship 4: parent_id=1, child_id=8, child=nnnn","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:52:33.741Z"}
{"level":"error","message":"AppError: Not authorized to access this child's apps","method":"GET","path":"/api/child-apps/child/2","service":"digital-wellbeing-api","stack":"AppError: Not authorized to access this child's apps\n    at exports.getChildApps (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\childAppController.js:27:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-13T13:52:34.152Z"}
{"level":"error","message":"AppError: Not authorized to access this child's apps","method":"GET","path":"/api/child-apps/child/8","service":"digital-wellbeing-api","stack":"AppError: Not authorized to access this child's apps\n    at exports.getChildApps (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\childAppController.js:27:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-13T13:53:48.334Z"}
{"level":"error","message":"AppError: Not authorized to access this child's apps","method":"GET","path":"/api/child-apps/child/8","service":"digital-wellbeing-api","stack":"AppError: Not authorized to access this child's apps\n    at exports.getChildApps (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\childAppController.js:27:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-13T13:54:33.968Z"}
{"level":"error","message":"AppError: Not authorized to access this child's apps","method":"GET","path":"/api/child-apps/child/8","service":"digital-wellbeing-api","stack":"AppError: Not authorized to access this child's apps\n    at exports.getChildApps (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\childAppController.js:27:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-13T13:54:37.459Z"}
{"level":"error","message":"AppError: Not authorized to access this child's apps","method":"GET","path":"/api/child-apps/child/8","service":"digital-wellbeing-api","stack":"AppError: Not authorized to access this child's apps\n    at exports.getChildApps (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\childAppController.js:27:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-13T13:54:39.416Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:54:57.065Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:54:57.182Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:54:57.224Z"}
{"level":"info","message":"User connected: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:55:00.409Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:55:19.976Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:55:20.055Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:55:20.059Z"}
{"level":"info","message":"User connected: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:55:23.614Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:55:41.746Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:55:41.830Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:55:41.835Z"}
{"level":"info","message":"User connected: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:55:44.772Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:56:03.207Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:56:03.306Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:56:03.310Z"}
{"level":"info","message":"User connected: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:56:04.502Z"}
{"level":"info","message":"Getting child apps for child ID: 3 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:56:32.716Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:56:32.720Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:56:32.725Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 3","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:56:32.728Z"}
{"level":"info","message":"Secondary guardian has read_only access","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:56:32.728Z"}
{"level":"info","message":"Getting child apps for child ID: 3 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:56:40.990Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:56:40.995Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:56:41.002Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 3","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:56:41.010Z"}
{"level":"info","message":"Secondary guardian has read_only access","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:56:41.012Z"}
{"level":"info","message":"Getting child apps for child ID: 3 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:57:11.816Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:57:11.821Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:57:11.825Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 3","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:57:11.828Z"}
{"level":"info","message":"Secondary guardian has read_only access","service":"digital-wellbeing-api","timestamp":"2025-04-13T13:57:11.828Z"}
{"level":"info","message":"Getting child apps for child ID: 3 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:04:23.210Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:04:23.214Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:04:23.219Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 3","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:04:23.221Z"}
{"level":"info","message":"Secondary guardian has read_only access","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:04:23.222Z"}
{"level":"info","message":"Getting child apps for child ID: 4 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:04:59.670Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:04:59.674Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:04:59.678Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 4","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:04:59.681Z"}
{"level":"info","message":"Secondary guardian has read_only access","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:04:59.681Z"}
{"level":"info","message":"Getting child apps for child ID: 4 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:05:01.437Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:05:01.439Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:05:01.441Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 4","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:05:01.443Z"}
{"level":"info","message":"Secondary guardian has read_only access","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:05:01.444Z"}
{"level":"info","message":"Getting child apps for child ID: 4 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:05:03.459Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:05:03.464Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:05:03.468Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 4","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:05:03.471Z"}
{"level":"info","message":"Secondary guardian has read_only access","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:05:03.472Z"}
{"level":"info","message":"Getting child apps for child ID: 8 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:05:07.161Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:05:07.165Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:05:07.167Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 8","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:05:07.170Z"}
{"level":"info","message":"Secondary guardian has read_only access","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:05:07.171Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:05:10.340Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:05:10.344Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:05:10.347Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 2","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:05:10.350Z"}
{"level":"info","message":"Secondary guardian has read_only access","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:05:10.350Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:05:17.586Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:05:17.588Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:05:17.590Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 2","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:05:17.592Z"}
{"level":"info","message":"Secondary guardian has read_only access","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:05:17.592Z"}
{"level":"info","message":"Getting child apps for child ID: 3 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:05:26.054Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:05:26.060Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:05:26.064Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 3","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:05:26.068Z"}
{"level":"info","message":"Secondary guardian has read_only access","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:05:26.068Z"}
{"level":"info","message":"Getting child apps for child ID: 3 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:14:45.834Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:14:45.839Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:14:45.843Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 3","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:14:45.845Z"}
{"level":"info","message":"Secondary guardian has read_only access","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:14:45.845Z"}
{"level":"info","message":"Getting child apps for child ID: 3 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:14:55.336Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:14:55.387Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:14:55.432Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 3","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:14:55.482Z"}
{"level":"info","message":"Secondary guardian has read_only access","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:14:55.513Z"}
{"level":"info","message":"Getting child apps for child ID: 3 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:17:13.507Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:17:13.511Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:17:13.515Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 3","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:17:13.517Z"}
{"level":"info","message":"Secondary guardian has read_only access","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:17:13.518Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:17:13.693Z"}
{"level":"info","message":"Secondary guardian has read-only access, denying update","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:17:13.697Z"}
{"level":"error","message":"AppError: Not authorized to update this child app","method":"PUT","path":"/api/child-apps/27","service":"digital-wellbeing-api","stack":"AppError: Not authorized to update this child app\n    at exports.updateChildApp (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\childAppController.js:298:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-13T14:17:13.698Z"}
{"level":"info","message":"Getting child apps for child ID: 3 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:17:25.058Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:17:25.063Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:17:25.067Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 3","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:17:25.069Z"}
{"level":"info","message":"Secondary guardian has read_only access","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:17:25.070Z"}
{"level":"info","message":"User disconnected: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:23:12.189Z"}
{"level":"info","message":"Getting accessible children for secondary guardian ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:23:49.387Z"}
{"level":"info","message":"Found 1 guardian relationships","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:23:49.395Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:23:49.396Z"}
{"level":"info","message":"User connected: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:23:49.402Z"}
{"level":"info","message":"Found 4 parent-child relationships","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:23:49.407Z"}
{"level":"info","message":"Relationship 1: parent_id=1, child_id=2, child=Child1","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:23:49.419Z"}
{"level":"info","message":"Relationship 2: parent_id=1, child_id=3, child=Child2","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:23:49.422Z"}
{"level":"info","message":"Relationship 3: parent_id=1, child_id=4, child=Child3","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:23:49.435Z"}
{"level":"info","message":"Relationship 4: parent_id=1, child_id=8, child=nnnn","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:23:49.441Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:23:49.906Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:23:49.910Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:23:49.914Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 2","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:23:49.916Z"}
{"level":"info","message":"Secondary guardian has read_only access","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:23:49.916Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:31:16.114Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:31:16.119Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:31:16.123Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 2","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:31:16.125Z"}
{"level":"info","message":"Secondary guardian has read_only access","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:31:16.126Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:31:18.793Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:31:18.795Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:31:18.797Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 2","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:31:18.799Z"}
{"level":"info","message":"Secondary guardian has read_only access","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:31:18.799Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:31:29.601Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:31:29.605Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:31:29.609Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 2","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:31:29.611Z"}
{"level":"info","message":"Secondary guardian has read_only access","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:31:29.612Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:31:33.004Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:31:33.007Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:31:33.009Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 2","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:31:33.011Z"}
{"level":"info","message":"Secondary guardian has read_only access","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:31:33.012Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:37:23.135Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:37:23.139Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:37:23.143Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 2","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:37:23.145Z"}
{"level":"info","message":"Secondary guardian has read_only access","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:37:23.146Z"}
{"level":"info","message":"User disconnected: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:40:53.938Z"}
{"level":"info","message":"Getting accessible children for secondary guardian ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:41:05.480Z"}
{"level":"info","message":"Found 1 guardian relationships","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:41:05.486Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:41:05.486Z"}
{"level":"info","message":"Found 4 parent-child relationships","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:41:05.494Z"}
{"level":"info","message":"Relationship 1: parent_id=1, child_id=2, child=Child1","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:41:05.496Z"}
{"level":"info","message":"Relationship 2: parent_id=1, child_id=3, child=Child2","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:41:05.497Z"}
{"level":"info","message":"Relationship 3: parent_id=1, child_id=4, child=Child3","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:41:05.497Z"}
{"level":"info","message":"Relationship 4: parent_id=1, child_id=8, child=nnnn","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:41:05.498Z"}
{"level":"info","message":"User connected: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:41:05.511Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:41:05.764Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:41:05.768Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:41:05.773Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 2","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:41:05.775Z"}
{"level":"info","message":"Secondary guardian has read_only access","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:41:05.776Z"}
{"level":"info","message":"User disconnected: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:43:47.197Z"}
{"level":"info","message":"Getting accessible children for secondary guardian ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:43:57.808Z"}
{"level":"info","message":"User connected: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:43:57.811Z"}
{"level":"info","message":"Found 1 guardian relationships","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:43:57.817Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:43:57.819Z"}
{"level":"info","message":"Found 4 parent-child relationships","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:43:57.826Z"}
{"level":"info","message":"Relationship 1: parent_id=1, child_id=2, child=Child1","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:43:57.836Z"}
{"level":"info","message":"Relationship 2: parent_id=1, child_id=3, child=Child2","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:43:57.838Z"}
{"level":"info","message":"Relationship 3: parent_id=1, child_id=4, child=Child3","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:43:57.839Z"}
{"level":"info","message":"Relationship 4: parent_id=1, child_id=8, child=nnnn","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:43:57.840Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:43:58.132Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:43:58.138Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:43:58.155Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 2","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:43:58.171Z"}
{"level":"info","message":"Secondary guardian has read_only access","service":"digital-wellbeing-api","timestamp":"2025-04-13T14:43:58.174Z"}
{"level":"info","message":"User disconnected: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T15:56:50.955Z"}
{"level":"info","message":"Getting accessible children for secondary guardian ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T15:57:02.281Z"}
{"level":"info","message":"Found 1 guardian relationships","service":"digital-wellbeing-api","timestamp":"2025-04-13T15:57:02.299Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T15:57:02.307Z"}
{"level":"info","message":"User connected: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T15:57:02.318Z"}
{"level":"info","message":"Found 4 parent-child relationships","service":"digital-wellbeing-api","timestamp":"2025-04-13T15:57:02.329Z"}
{"level":"info","message":"Relationship 1: parent_id=1, child_id=2, child=Child1","service":"digital-wellbeing-api","timestamp":"2025-04-13T15:57:02.337Z"}
{"level":"info","message":"Relationship 2: parent_id=1, child_id=3, child=Child2","service":"digital-wellbeing-api","timestamp":"2025-04-13T15:57:02.342Z"}
{"level":"info","message":"Relationship 3: parent_id=1, child_id=4, child=Child3","service":"digital-wellbeing-api","timestamp":"2025-04-13T15:57:02.342Z"}
{"level":"info","message":"Relationship 4: parent_id=1, child_id=8, child=nnnn","service":"digital-wellbeing-api","timestamp":"2025-04-13T15:57:02.349Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T15:57:02.614Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-13T15:57:02.619Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T15:57:02.625Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 2","service":"digital-wellbeing-api","timestamp":"2025-04-13T15:57:02.627Z"}
{"level":"info","message":"Secondary guardian has read_only access","service":"digital-wellbeing-api","timestamp":"2025-04-13T15:57:02.628Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T15:57:18.536Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-13T15:57:18.543Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T15:57:18.550Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 2","service":"digital-wellbeing-api","timestamp":"2025-04-13T15:57:18.552Z"}
{"level":"info","message":"Secondary guardian has read_only access","service":"digital-wellbeing-api","timestamp":"2025-04-13T15:57:18.553Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T15:57:21.011Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-13T15:57:21.015Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T15:57:21.019Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 2","service":"digital-wellbeing-api","timestamp":"2025-04-13T15:57:21.025Z"}
{"level":"info","message":"Secondary guardian has read_only access","service":"digital-wellbeing-api","timestamp":"2025-04-13T15:57:21.027Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T15:57:53.044Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-13T15:57:53.052Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T15:57:53.058Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 2","service":"digital-wellbeing-api","timestamp":"2025-04-13T15:57:53.061Z"}
{"level":"info","message":"Secondary guardian has read_only access","service":"digital-wellbeing-api","timestamp":"2025-04-13T15:57:53.062Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T15:58:09.846Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-13T15:58:09.853Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T15:58:09.860Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 2","service":"digital-wellbeing-api","timestamp":"2025-04-13T15:58:09.893Z"}
{"level":"info","message":"Secondary guardian has read_only access","service":"digital-wellbeing-api","timestamp":"2025-04-13T15:58:09.896Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T16:07:22.816Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-13T16:07:22.824Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T16:07:22.831Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 2","service":"digital-wellbeing-api","timestamp":"2025-04-13T16:07:22.834Z"}
{"level":"info","message":"Secondary guardian has read_only access","service":"digital-wellbeing-api","timestamp":"2025-04-13T16:07:22.835Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T16:07:28.177Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-13T16:07:28.184Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T16:07:28.189Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 2","service":"digital-wellbeing-api","timestamp":"2025-04-13T16:07:28.196Z"}
{"level":"info","message":"Secondary guardian has read_only access","service":"digital-wellbeing-api","timestamp":"2025-04-13T16:07:28.198Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T16:07:35.103Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-13T16:07:35.105Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T16:07:35.107Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 2","service":"digital-wellbeing-api","timestamp":"2025-04-13T16:07:35.109Z"}
{"level":"info","message":"Secondary guardian has read_only access","service":"digital-wellbeing-api","timestamp":"2025-04-13T16:07:35.109Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T16:07:39.096Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-13T16:07:39.099Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T16:07:39.102Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 2","service":"digital-wellbeing-api","timestamp":"2025-04-13T16:07:39.106Z"}
{"level":"info","message":"Secondary guardian has read_only access","service":"digital-wellbeing-api","timestamp":"2025-04-13T16:07:39.108Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T16:07:40.843Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-13T16:07:40.847Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T16:07:40.851Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 2","service":"digital-wellbeing-api","timestamp":"2025-04-13T16:07:40.857Z"}
{"level":"info","message":"Secondary guardian has read_only access","service":"digital-wellbeing-api","timestamp":"2025-04-13T16:07:40.858Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-13T16:07:44.324Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-13T16:07:44.328Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-13T16:07:44.333Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 2","service":"digital-wellbeing-api","timestamp":"2025-04-13T16:07:44.338Z"}
{"level":"info","message":"Secondary guardian has read_only access","service":"digital-wellbeing-api","timestamp":"2025-04-13T16:07:44.340Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T16:41:43.090Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-13T16:41:43.184Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-13T16:41:43.188Z"}
{"level":"info","message":"Migration 20240501_initial_migration already executed, skipping","service":"digital-wellbeing-api","timestamp":"2025-04-19T05:54:10.889Z"}
{"level":"info","message":"Migration 20240601_add_guardian_tables already executed, skipping","service":"digital-wellbeing-api","timestamp":"2025-04-19T05:54:10.892Z"}
{"level":"info","message":"All migrations completed successfully","service":"digital-wellbeing-api","timestamp":"2025-04-19T05:54:10.892Z"}
{"level":"info","message":"Migration 20240501_initial_migration already executed, skipping","service":"digital-wellbeing-api","timestamp":"2025-04-19T05:57:23.237Z"}
{"level":"info","message":"Migration 20240601_add_guardian_tables already executed, skipping","service":"digital-wellbeing-api","timestamp":"2025-04-19T05:57:23.241Z"}
{"level":"info","message":"Migration 20240501_initial_migration already executed, skipping","service":"digital-wellbeing-api","timestamp":"2025-04-19T05:58:28.742Z"}
{"level":"info","message":"Migration 20240601_add_guardian_tables already executed, skipping","service":"digital-wellbeing-api","timestamp":"2025-04-19T05:58:28.745Z"}
{"level":"info","message":"Running migration: 20240701_add_registration_code_to_users","service":"digital-wellbeing-api","timestamp":"2025-04-19T05:58:28.747Z"}
{"level":"info","message":"Starting migration to add registration code fields to users table","service":"digital-wellbeing-api","timestamp":"2025-04-19T05:58:28.748Z"}
{"level":"info","message":"Migration to add registration code fields completed successfully","service":"digital-wellbeing-api","timestamp":"2025-04-19T05:58:28.855Z"}
{"level":"info","message":"Migration 20240701_add_registration_code_to_users completed successfully","service":"digital-wellbeing-api","timestamp":"2025-04-19T05:58:28.870Z"}
{"level":"info","message":"All migrations completed successfully","service":"digital-wellbeing-api","timestamp":"2025-04-19T05:58:28.871Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:03:13.723Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:03:13.825Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:03:13.829Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:04:11.392Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:04:11.479Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:04:11.485Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:04:37.234Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:04:37.311Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:04:37.314Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:05:19.020Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:05:19.105Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:05:19.109Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:06:01.432Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:06:01.514Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:06:01.518Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:06:52.843Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:06:52.925Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:06:52.929Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:10:38.289Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:10:38.386Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:10:38.392Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:15:42.193Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:15:42.238Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:15:42.245Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:15:51.113Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:15:51.117Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:19:01.029Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:19:01.135Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:19:01.140Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:19:03.046Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:19:12.481Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:20:56.023Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:20:56.110Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:20:56.113Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:22:05.417Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:22:05.464Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:22:05.468Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:22:11.657Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:22:11.660Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:22:40.810Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:22:40.815Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:22:40.880Z"}
{"level":"info","message":"Getting child apps for child ID: 13 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:24:22.705Z"}
{"level":"info","message":"User 1 has direct relationship with child 13","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:24:22.714Z"}
{"level":"info","message":"Migration 20240501_initial_migration already executed, skipping","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:31:01.350Z"}
{"level":"info","message":"Migration 20240601_add_guardian_tables already executed, skipping","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:31:01.353Z"}
{"level":"info","message":"Migration 20240701_add_registration_code_to_users already executed, skipping","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:31:01.355Z"}
{"level":"info","message":"All migrations completed successfully","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:31:01.356Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:32:28.990Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:32:29.108Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:32:29.115Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:32:32.471Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:33:42.667Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:33:42.764Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:33:42.768Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:33:44.704Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:34:23.347Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:34:23.428Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:34:23.432Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:34:23.882Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:34:54.243Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:34:54.324Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:34:54.328Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:34:56.950Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:35:13.677Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:35:13.756Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:35:13.760Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:35:18.477Z"}
{"level":"error","message":"AppError: Invalid credentials","method":"POST","path":"/api/auth/login","service":"digital-wellbeing-api","stack":"AppError: Invalid credentials\n    at exports.login (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\authController.js:143:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-19T06:35:23.084Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:35:35.202Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:35:35.297Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:35:35.301Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:35:37.669Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:36:23.455Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:36:23.612Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:36:23.698Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:36:25.871Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:50:23.656Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:50:33.165Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:50:33.254Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:50:33.260Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:50:38.494Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:50:39.552Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T06:50:39.560Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:08:03.195Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:08:03.274Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:08:03.279Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:08:05.394Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:08:14.716Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:08:14.797Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:08:14.802Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:08:17.460Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:18:22.389Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:18:22.467Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:18:22.470Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:18:34.615Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:18:34.698Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:19:20.369Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:19:20.444Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:19:30.508Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:19:30.596Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:19:54.422Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:19:54.499Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:20:23.944Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:20:24.072Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:20:24.077Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:20:38.671Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:20:38.760Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:21:03.966Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:21:04.048Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:22:05.198Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:22:05.279Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:22:05.283Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:22:24.779Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:22:24.864Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:22:57.134Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:22:57.223Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:23:24.698Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:23:24.794Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:23:42.226Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:23:42.321Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:23:51.918Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:23:52.028Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:23:52.033Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:24:10.588Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:24:10.675Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:24:10.678Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:26:37.469Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:26:37.522Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T07:26:37.525Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T08:22:35.371Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T08:22:42.448Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T08:22:42.452Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T08:22:42.454Z"}
{"level":"info","message":"Getting child apps for child ID: 13 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T08:22:48.935Z"}
{"level":"info","message":"User 1 has direct relationship with child 13","service":"digital-wellbeing-api","timestamp":"2025-04-19T08:22:48.942Z"}
{"level":"error","message":"Error uploading app usage data: column \"usage_time_ms\" of relation \"child_apps\" does not exist","service":"digital-wellbeing-api","timestamp":"2025-04-19T08:49:29.312Z"}
{"level":"error","message":"Error: Failed to upload app usage data","method":"POST","path":"/api/app-usage/upload","service":"digital-wellbeing-api","stack":"Error: Failed to upload app usage data\n    at exports.uploadAppUsage (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\appUsageController.js:96:17)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-19T08:49:29.315Z"}
{"level":"error","message":"Error uploading app usage data: column \"usage_time_ms\" of relation \"child_apps\" does not exist","service":"digital-wellbeing-api","timestamp":"2025-04-19T08:49:29.321Z"}
{"level":"error","message":"Error: Failed to upload app usage data","method":"POST","path":"/api/app-usage/upload","service":"digital-wellbeing-api","stack":"Error: Failed to upload app usage data\n    at exports.uploadAppUsage (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\appUsageController.js:96:17)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-19T08:49:29.323Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T08:53:39.994Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T08:53:40.152Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T08:53:40.158Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T08:53:40.328Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T08:54:31.723Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T08:54:31.822Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T08:54:31.826Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T08:54:34.904Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T08:55:00.506Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T08:55:00.622Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T08:55:15.951Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T08:55:16.074Z"}
{"level":"error","message":"Error uploading app usage data: column \"last_used_timestamp\" of relation \"child_apps\" does not exist","service":"digital-wellbeing-api","timestamp":"2025-04-19T08:56:25.714Z"}
{"level":"error","message":"Error: Failed to upload app usage data","method":"POST","path":"/api/app-usage/upload","service":"digital-wellbeing-api","stack":"Error: Failed to upload app usage data\n    at exports.uploadAppUsage (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\appUsageController.js:96:17)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-19T08:56:25.721Z"}
{"level":"error","message":"Error uploading app usage data: column \"last_used_timestamp\" of relation \"child_apps\" does not exist","service":"digital-wellbeing-api","timestamp":"2025-04-19T08:56:25.742Z"}
{"level":"error","message":"Error: Failed to upload app usage data","method":"POST","path":"/api/app-usage/upload","service":"digital-wellbeing-api","stack":"Error: Failed to upload app usage data\n    at exports.uploadAppUsage (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\appUsageController.js:96:17)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-19T08:56:25.744Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T08:57:54.544Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T08:57:54.679Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T08:57:54.694Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T08:57:56.475Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T08:58:22.412Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T08:58:22.564Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T08:58:40.061Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T08:58:40.151Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T09:03:01.355Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T09:03:01.451Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T09:03:01.455Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T09:03:01.911Z"}
{"level":"info","message":"App usage data uploaded for user 13","service":"digital-wellbeing-api","timestamp":"2025-04-19T09:03:30.876Z"}
{"level":"error","message":"Error uploading app usage data: Validation error","service":"digital-wellbeing-api","timestamp":"2025-04-19T09:03:30.890Z"}
{"level":"error","message":"Error: Failed to upload app usage data","method":"POST","path":"/api/app-usage/upload","service":"digital-wellbeing-api","stack":"Error: Failed to upload app usage data\n    at exports.uploadAppUsage (C:\\Users\\<USER>\\Desktop\\DigitalWellbeing\\backend\\controllers\\appUsageController.js:95:17)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-04-19T09:03:30.892Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T09:04:37.173Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T09:04:37.313Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T09:04:37.318Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T09:04:37.836Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T09:04:50.216Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T09:04:50.357Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T09:04:50.364Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T09:04:50.692Z"}
{"level":"info","message":"Getting child apps for child ID: 13 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T09:05:33.190Z"}
{"level":"info","message":"User 1 has direct relationship with child 13","service":"digital-wellbeing-api","timestamp":"2025-04-19T09:05:33.199Z"}
{"level":"info","message":"Getting child apps for child ID: 13 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T09:05:57.297Z"}
{"level":"info","message":"User 1 has direct relationship with child 13","service":"digital-wellbeing-api","timestamp":"2025-04-19T09:05:57.306Z"}
{"level":"info","message":"App usage data uploaded for user 13","service":"digital-wellbeing-api","timestamp":"2025-04-19T09:07:10.344Z"}
{"level":"info","message":"App usage data uploaded for user 13","service":"digital-wellbeing-api","timestamp":"2025-04-19T09:07:10.430Z"}
{"level":"info","message":"Getting child apps for child ID: 13 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T09:07:22.298Z"}
{"level":"info","message":"User 1 has direct relationship with child 13","service":"digital-wellbeing-api","timestamp":"2025-04-19T09:07:22.303Z"}
{"level":"info","message":"Getting child apps for child ID: 13 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T09:07:27.518Z"}
{"level":"info","message":"User 1 has direct relationship with child 13","service":"digital-wellbeing-api","timestamp":"2025-04-19T09:07:27.521Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T09:09:42.235Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T09:09:42.324Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T09:09:42.330Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T09:09:44.274Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:09:19.563Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:09:19.731Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:09:19.769Z"}
{"level":"error","message":"Error: incorrect header check","method":"POST","path":"/api/app-usage/upload","service":"digital-wellbeing-api","stack":"Error: incorrect header check\n    at genericNodeError (node:internal/errors:983:15)\n    at wrappedFn (node:internal/errors:537:14)\n    at Zlib.zlibOnError [as onerror] (node:zlib:185:17)","timestamp":"2025-04-19T10:09:59.603Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:12:33.628Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:12:33.716Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:12:33.720Z"}
{"level":"info","message":"App usage data uploaded for user 13","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:12:43.531Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:15:02.721Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:15:02.784Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:15:02.791Z"}
{"level":"info","message":"Getting child apps for child ID: 13 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:15:12.172Z"}
{"level":"info","message":"User 1 has direct relationship with child 13","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:15:12.175Z"}
{"level":"info","message":"Getting child apps for child ID: 13 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:16:16.211Z"}
{"level":"info","message":"User 1 has direct relationship with child 13","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:16:16.217Z"}
{"level":"info","message":"Getting child apps for child ID: 13 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:16:37.153Z"}
{"level":"info","message":"User 1 has direct relationship with child 13","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:16:37.158Z"}
{"level":"info","message":"User 1 has direct relationship with child 13","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:16:37.207Z"}
{"level":"info","message":"Getting child apps for child ID: 13 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:16:53.934Z"}
{"level":"info","message":"User 1 has direct relationship with child 13","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:16:53.938Z"}
{"level":"info","message":"User 1 has direct relationship with child 13","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:16:53.990Z"}
{"level":"info","message":"Getting child apps for child ID: 13 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:18:46.822Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:18:46.827Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:18:46.840Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 13","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:18:46.842Z"}
{"level":"info","message":"Secondary guardian has read_only access","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:18:46.843Z"}
{"level":"info","message":"Getting child apps for child ID: 13 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:18:51.026Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:18:51.028Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:18:51.030Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 13","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:18:51.033Z"}
{"level":"info","message":"Secondary guardian has read_only access","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:18:51.033Z"}
{"level":"info","message":"Getting child apps for child ID: 13 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:18:59.844Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:18:59.846Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:18:59.848Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 13","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:18:59.850Z"}
{"level":"info","message":"Secondary guardian has read_only access","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:18:59.853Z"}
{"level":"info","message":"Getting child apps for child ID: 13 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:19:25.292Z"}
{"level":"info","message":"User 1 has direct relationship with child 13","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:19:25.296Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:23:47.654Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:24:57.130Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:24:57.196Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:24:57.199Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:25:11.455Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:25:11.461Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:25:28.326Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:25:28.328Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:25:28.334Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:25:28.336Z"}
{"level":"info","message":"Secondary guardian has read_only access","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:25:28.338Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:25:39.434Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:25:39.439Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:25:39.449Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:25:39.451Z"}
{"level":"info","message":"Secondary guardian has read_only access","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:25:39.453Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:27:51.590Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:28:12.471Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:28:12.565Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:28:12.568Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:29:45.363Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:29:45.414Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:29:45.418Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:30:07.715Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:30:07.717Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:30:07.720Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:30:07.724Z"}
{"level":"info","message":"Secondary guardian has read_only access","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:30:07.726Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:30:16.498Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:36:51.814Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:36:51.932Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:36:51.937Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:39:10.385Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:39:10.510Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:39:10.515Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:39:20.057Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:39:20.059Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:40:53.566Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:40:53.571Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:40:53.578Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:40:53.580Z"}
{"level":"info","message":"Secondary guardian has read_only access","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:40:53.581Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:51:48.023Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:51:48.100Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:51:48.104Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:52:13.055Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:52:13.140Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:52:13.144Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:52:44.755Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:52:44.844Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:52:44.849Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:53:11.901Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:53:11.994Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:53:11.998Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:53:25.765Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:53:25.845Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:53:25.848Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:54:01.024Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:54:01.110Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:54:01.114Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:54:43.516Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:54:43.601Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:54:43.605Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:55:02.025Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:55:02.119Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:55:02.124Z"}
{"level":"info","message":"Migration 20240501_initial_migration already executed, skipping","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:55:31.468Z"}
{"level":"info","message":"Migration 20240601_add_guardian_tables already executed, skipping","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:55:31.485Z"}
{"level":"info","message":"Migration 20240701_add_registration_code_to_users already executed, skipping","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:55:31.490Z"}
{"level":"info","message":"All migrations completed successfully","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:55:31.491Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:56:12.040Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:56:12.117Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:56:12.123Z"}
{"level":"info","message":"Migration 20240501_initial_migration already executed, skipping","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:56:26.958Z"}
{"level":"info","message":"Migration 20240601_add_guardian_tables already executed, skipping","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:56:26.963Z"}
{"level":"info","message":"Running migration: 20240619_add_access_level_to_users","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:56:26.966Z"}
{"level":"info","message":"Added access_level column to users table","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:56:27.030Z"}
{"level":"info","message":"Migration 20240619_add_access_level_to_users completed successfully","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:56:27.034Z"}
{"level":"info","message":"Migration 20240701_add_registration_code_to_users already executed, skipping","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:56:27.035Z"}
{"level":"info","message":"All migrations completed successfully","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:56:27.036Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:56:42.358Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:56:42.446Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:57:12.090Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:57:12.168Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:57:24.888Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:57:24.972Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:57:42.053Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:57:42.169Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:59:55.123Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:59:55.266Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T10:59:55.285Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:00:04.151Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:00:04.155Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:00:22.834Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:00:22.845Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:00:22.856Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:00:22.860Z"}
{"level":"info","message":"Secondary guardian has access_level=false","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:00:22.861Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:11:49.100Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:11:57.799Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:11:57.919Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:11:57.928Z"}
{"level":"info","message":"Getting accessible children for secondary guardian ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:14:10.633Z"}
{"level":"info","message":"Found 1 guardian relationships","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:14:10.653Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:14:10.654Z"}
{"level":"info","message":"Found 5 parent-child relationships","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:14:10.667Z"}
{"level":"info","message":"Relationship 1: parent_id=1, child_id=2, child=Child1","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:14:10.668Z"}
{"level":"info","message":"Relationship 2: parent_id=1, child_id=3, child=Child2","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:14:10.669Z"}
{"level":"info","message":"Relationship 3: parent_id=1, child_id=4, child=Child3","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:14:10.670Z"}
{"level":"info","message":"Relationship 4: parent_id=1, child_id=8, child=nnnn","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:14:10.671Z"}
{"level":"info","message":"Relationship 5: parent_id=1, child_id=13, child=testRegCode","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:14:10.671Z"}
{"level":"info","message":"User connected: 12","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:14:10.768Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:14:10.885Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:14:10.895Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:14:10.935Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:14:10.944Z"}
{"level":"info","message":"Secondary guardian has access_level=false","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:14:10.945Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:14:38.390Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:14:38.396Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:14:38.401Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:14:38.403Z"}
{"level":"info","message":"Secondary guardian has access_level=false","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:14:38.404Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:15:12.247Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:15:12.252Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:18:02.423Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:18:02.595Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:18:02.609Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:20:44.817Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:20:44.907Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:20:44.911Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:21:57.661Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:21:57.811Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:21:57.818Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:23:46.142Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:23:46.238Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:23:46.243Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:23:59.635Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:23:59.730Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:23:59.734Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:24:19.984Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:24:20.100Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:24:20.107Z"}
{"level":"info","message":"Getting accessible children for secondary guardian ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:27:45.318Z"}
{"level":"info","message":"Found 1 guardian relationships","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:27:45.336Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:27:45.336Z"}
{"level":"info","message":"Found 5 parent-child relationships","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:27:45.348Z"}
{"level":"info","message":"Relationship 1: parent_id=1, child_id=2, child=Child1","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:27:45.349Z"}
{"level":"info","message":"Relationship 2: parent_id=1, child_id=3, child=Child2","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:27:45.349Z"}
{"level":"info","message":"Relationship 3: parent_id=1, child_id=4, child=Child3","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:27:45.350Z"}
{"level":"info","message":"Relationship 4: parent_id=1, child_id=8, child=nnnn","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:27:45.350Z"}
{"level":"info","message":"Relationship 5: parent_id=1, child_id=13, child=testRegCode","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:27:45.351Z"}
{"level":"info","message":"User 12 has access_level=false (read_only)","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:27:45.352Z"}
{"level":"info","message":"User 12 has access_level=false (read_only)","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:27:45.352Z"}
{"level":"info","message":"User 12 has access_level=false (read_only)","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:27:45.353Z"}
{"level":"info","message":"User 12 has access_level=false (read_only)","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:27:45.354Z"}
{"level":"info","message":"User 12 has access_level=false (read_only)","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:27:45.354Z"}
{"level":"info","message":"User connected: 12","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:27:45.487Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:27:45.623Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:27:45.630Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:27:45.639Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:27:45.642Z"}
{"level":"info","message":"Secondary guardian has access_level=false","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:27:45.647Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:28:26.294Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:28:26.299Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:32:14.059Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:32:14.213Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:32:14.218Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:32:27.980Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:32:28.126Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:32:28.134Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:32:49.377Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:32:49.540Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:32:49.547Z"}
{"level":"info","message":"User 1 has access_level=true (full_access)","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:39:57.287Z"}
{"level":"info","message":"User 1 has access_level=true (full_access)","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:39:57.288Z"}
{"level":"info","message":"User 1 has access_level=true (full_access)","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:39:57.289Z"}
{"level":"info","message":"User 1 has access_level=true (full_access)","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:39:57.289Z"}
{"level":"info","message":"User 1 has access_level=true (full_access)","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:39:57.289Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:39:57.396Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:39:57.543Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:39:57.548Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:40:38.623Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:40:38.630Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:40:38.640Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:40:38.643Z"}
{"level":"info","message":"Secondary guardian has access_level=false","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:40:38.647Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:41:40.785Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:43:09.613Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:43:09.749Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:43:09.753Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:43:19.529Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:43:19.651Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:43:19.658Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:43:30.612Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:43:30.724Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:43:30.729Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:43:54.958Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:43:55.060Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:43:55.066Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:44:12.808Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:44:12.922Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:44:12.926Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:44:24.706Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:44:24.802Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:44:24.806Z"}
{"level":"info","message":"Getting accessible children for secondary guardian ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:49:56.846Z"}
{"level":"info","message":"Found 1 guardian relationships","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:49:56.861Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:49:56.862Z"}
{"level":"info","message":"Found 5 parent-child relationships","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:49:56.873Z"}
{"level":"info","message":"Relationship 1: parent_id=1, child_id=2, child=Child1","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:49:56.875Z"}
{"level":"info","message":"Relationship 2: parent_id=1, child_id=3, child=Child2","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:49:56.875Z"}
{"level":"info","message":"Relationship 3: parent_id=1, child_id=4, child=Child3","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:49:56.876Z"}
{"level":"info","message":"Relationship 4: parent_id=1, child_id=8, child=nnnn","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:49:56.876Z"}
{"level":"info","message":"Relationship 5: parent_id=1, child_id=13, child=testRegCode","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:49:56.877Z"}
{"level":"info","message":"User 12 has access_level=false (false)","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:49:56.878Z"}
{"level":"info","message":"User 12 has access_level=false (false)","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:49:56.878Z"}
{"level":"info","message":"User 12 has access_level=false (false)","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:49:56.879Z"}
{"level":"info","message":"User 12 has access_level=false (false)","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:49:56.880Z"}
{"level":"info","message":"User 12 has access_level=false (false)","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:49:56.881Z"}
{"level":"info","message":"User connected: 12","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:49:56.986Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:49:57.135Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:49:57.154Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:49:57.169Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:49:57.181Z"}
{"level":"info","message":"Secondary guardian has access_level=false","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:49:57.184Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:50:18.127Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:50:18.132Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 12","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:51:10.515Z"}
{"level":"info","message":"User 12 is a secondary guardian, checking access rights","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:51:10.521Z"}
{"level":"info","message":"Main guardian IDs: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:51:10.526Z"}
{"level":"info","message":"Found relationship between main guardian 1 and child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:51:10.531Z"}
{"level":"info","message":"Secondary guardian has access_level=false","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:51:10.532Z"}
{"level":"info","message":"User disconnected: 12","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:51:36.347Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:52:42.685Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:52:42.793Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T11:52:42.798Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T12:51:17.331Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T12:51:17.442Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T12:51:17.445Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T12:58:40.880Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T12:58:40.889Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T12:58:40.890Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T12:58:40.891Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T12:58:40.892Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T12:58:41.617Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T12:58:41.620Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:02:01.414Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:02:01.445Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:02:01.459Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:03:00.402Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:03:00.417Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:03:13.423Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:03:13.424Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:03:13.425Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:03:13.425Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:03:13.426Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:03:14.932Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:03:14.933Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:03:14.934Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:03:14.935Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:03:14.935Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:03:17.248Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:03:17.270Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:03:18.867Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:03:18.870Z"}
{"level":"info","message":"Getting child apps for child ID: 13 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:03:32.265Z"}
{"level":"info","message":"User 1 has direct relationship with child 13","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:03:32.270Z"}
{"level":"info","message":"Getting child apps for child ID: 8 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:03:35.258Z"}
{"level":"info","message":"User 1 has direct relationship with child 8","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:03:35.261Z"}
{"level":"info","message":"Getting child apps for child ID: 3 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:03:38.656Z"}
{"level":"info","message":"User 1 has direct relationship with child 3","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:03:38.660Z"}
{"level":"info","message":"Getting child apps for child ID: 4 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:03:41.567Z"}
{"level":"info","message":"User 1 has direct relationship with child 4","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:03:41.571Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:04:53.999Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:04:54.000Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:04:54.001Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:04:54.001Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:04:54.002Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:05:15.692Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:05:15.698Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:05:16.416Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:05:16.419Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:05:32.064Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:19:00.604Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:19:00.604Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:19:00.605Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:19:00.605Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:19:00.606Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:19:01.597Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:19:01.599Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:23:02.129Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:23:02.130Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:23:02.130Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:23:02.130Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:23:02.131Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:23:04.039Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:23:04.042Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:23:06.065Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:23:06.068Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:24:04.351Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:24:04.421Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:24:04.422Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:24:04.425Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:24:04.425Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:24:06.554Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:24:06.556Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:24:08.554Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:24:08.556Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:30:20.684Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:30:20.684Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:30:20.685Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:30:20.685Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:30:20.686Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:30:21.843Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:30:21.845Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:31:58.995Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:31:58.996Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:31:58.997Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:31:58.997Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:31:58.998Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:32:00.431Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:32:00.433Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:34:51.981Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:34:51.982Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:34:51.982Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:34:51.983Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:34:51.984Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:34:52.674Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:34:52.676Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:38:08.317Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:38:08.317Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:38:08.318Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:38:08.318Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:38:08.319Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:38:09.259Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:38:09.261Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:38:27.839Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:38:27.840Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:38:27.840Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:38:27.841Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:38:27.842Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:38:29.390Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:38:29.392Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:38:31.396Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:38:31.398Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:41:21.903Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:41:21.904Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:41:21.904Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:41:21.905Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:41:21.905Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:41:24.033Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:41:24.035Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:41:26.062Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:41:26.064Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:44:12.692Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:44:12.692Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:44:12.693Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:44:12.693Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:44:12.694Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:44:15.743Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:44:15.748Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:44:17.578Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:44:17.594Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:47:51.831Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:47:51.831Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:47:51.832Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:47:51.832Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:47:51.833Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:47:53.302Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:47:53.304Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:47:55.294Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:47:55.296Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:48:07.149Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:48:07.150Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:48:07.151Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:48:07.151Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:48:07.152Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:48:12.873Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:48:12.874Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:48:14.872Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:48:14.874Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:48:26.712Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:48:26.712Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:48:26.713Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:48:26.713Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:48:26.714Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:48:41.169Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:48:41.173Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:48:42.924Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:48:42.926Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:49:07.425Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:49:07.426Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:49:07.427Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:49:07.428Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:49:07.429Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:49:09.961Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:49:09.962Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:49:11.931Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:49:11.942Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:51:31.575Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:51:31.576Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:51:31.577Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:51:31.577Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:51:31.578Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:51:35.124Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:51:35.130Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:51:37.110Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:51:37.112Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:52:02.946Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:52:02.946Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:52:02.947Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:52:02.948Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:52:02.948Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:52:03.620Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:52:03.623Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:55:45.456Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:55:45.456Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:55:45.457Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:55:45.457Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:55:45.457Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:55:48.600Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:55:48.601Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:55:50.599Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:55:50.601Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:57:22.749Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:57:22.750Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:57:22.751Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:57:22.751Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:57:22.752Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:57:28.172Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:57:28.176Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:57:30.173Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:57:30.176Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:58:12.483Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:58:12.489Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:58:12.489Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:58:12.489Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:58:12.490Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:58:16.345Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:58:16.349Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:58:18.352Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:58:18.354Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:58:33.234Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:58:33.235Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:58:33.236Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:58:33.237Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:58:33.237Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:58:35.189Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:58:35.192Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:58:37.149Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:58:37.152Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:59:36.920Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:59:36.921Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:59:36.922Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:59:36.922Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:59:36.923Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:59:38.441Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:59:38.443Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:59:40.391Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T13:59:40.394Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:02:19.370Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:02:19.370Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:02:19.371Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:02:19.371Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:02:19.372Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:02:21.232Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:02:21.327Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:02:23.166Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:02:23.168Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:03:43.552Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:03:43.654Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:03:43.661Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:09:28.906Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:09:29.033Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:09:29.040Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:15:36.847Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:15:36.849Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:15:36.849Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:15:36.850Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:15:36.851Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:15:37.753Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:15:37.759Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:17:35.891Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:17:35.892Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:17:35.893Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:17:35.894Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:17:35.895Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:17:37.073Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:17:37.078Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:19:32.362Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:19:32.416Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:19:32.417Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:19:32.418Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:19:32.419Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:19:33.461Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:19:33.464Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:20:24.603Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:20:24.605Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:20:24.606Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:20:24.607Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:20:24.608Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:20:25.754Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:20:25.759Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:20:40.258Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:20:40.259Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:20:40.260Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:20:40.261Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:20:40.262Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:20:41.277Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:20:41.281Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:21:21.754Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:21:21.755Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:21:21.756Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:21:21.758Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:21:21.759Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:21:23.397Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:21:23.400Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:21:42.553Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:21:42.561Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:21:42.563Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:21:42.564Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:21:42.571Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:21:43.528Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:21:43.554Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:28:00.443Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:28:00.445Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:28:00.446Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:28:00.447Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:28:00.448Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:28:01.497Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:28:01.500Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:31:08.254Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:31:08.262Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:31:08.263Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:31:08.264Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:31:08.265Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:31:09.998Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:31:10.002Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:31:11.998Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:31:12.004Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:32:53.989Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:32:53.990Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:32:53.991Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:32:53.992Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:32:53.993Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:32:54.653Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:32:54.656Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:33:22.950Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:33:22.952Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:33:22.953Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:33:22.955Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:33:22.956Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:33:24.769Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:33:24.772Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:33:26.742Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:33:26.746Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:35:23.614Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:35:23.615Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:35:23.616Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:35:23.617Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:35:23.618Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:35:24.582Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:35:24.586Z"}
{"level":"info","message":"Getting child apps for child ID: 3 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:35:39.223Z"}
{"level":"info","message":"User 1 has direct relationship with child 3","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:35:39.232Z"}
{"level":"info","message":"Getting child apps for child ID: 4 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:35:43.065Z"}
{"level":"info","message":"User 1 has direct relationship with child 4","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:35:43.077Z"}
{"level":"info","message":"Getting child apps for child ID: 8 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:35:52.025Z"}
{"level":"info","message":"User 1 has direct relationship with child 8","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:35:52.029Z"}
{"level":"info","message":"Getting child apps for child ID: 13 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:35:55.942Z"}
{"level":"info","message":"User 1 has direct relationship with child 13","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:35:55.948Z"}
{"level":"info","message":"Getting child apps for child ID: 13 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:36:05.874Z"}
{"level":"info","message":"User 1 has direct relationship with child 13","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:36:05.879Z"}
{"level":"info","message":"Getting child apps for child ID: 13 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:36:13.128Z"}
{"level":"info","message":"User 1 has direct relationship with child 13","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:36:13.182Z"}
{"level":"info","message":"Getting child apps for child ID: 13 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:36:15.635Z"}
{"level":"info","message":"User 1 has direct relationship with child 13","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:36:15.638Z"}
{"level":"info","message":"Getting child apps for child ID: 13 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:36:23.881Z"}
{"level":"info","message":"User 1 has direct relationship with child 13","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:36:23.883Z"}
{"level":"info","message":"Getting child apps for child ID: 13 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:36:26.565Z"}
{"level":"info","message":"User 1 has direct relationship with child 13","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:36:26.568Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:36:26.689Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:39:01.179Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:39:01.180Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:39:01.181Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:39:01.182Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:39:01.183Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:39:58.319Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:39:58.325Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:39:59.635Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:39:59.638Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:40:04.237Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:40:04.240Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:40:04.286Z"}
{"level":"info","message":"App usage data uploaded for user 13","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:42:44.005Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:47:27.089Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:47:27.295Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:47:44.625Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:47:44.628Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:47:44.629Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:47:44.630Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:47:44.631Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:47:46.279Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:47:46.281Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:47:48.313Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:47:48.317Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:47:51.824Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:47:51.827Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:47:52.768Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:48:11.721Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:48:11.726Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:48:53.239Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:48:53.247Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:48:55.140Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:48:55.143Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:48:57.978Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:48:57.980Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:57:21.083Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:58:38.013Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:58:38.018Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:58:38.019Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:58:38.020Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:58:38.020Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:58:39.681Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:58:39.684Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:58:41.656Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:58:41.658Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:58:58.088Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:58:58.093Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:58:58.098Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:59:06.984Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:59:06.990Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T14:59:47.780Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:03:51.278Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:03:51.376Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:03:51.382Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:06:04.228Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:06:04.229Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:06:04.230Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:06:04.230Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:06:04.231Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:06:05.142Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:06:05.153Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:06:10.536Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:06:10.539Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:06:11.120Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:06:14.096Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:06:14.102Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:06:19.235Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:06:19.238Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:07:36.276Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:07:36.277Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:07:36.280Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:07:36.281Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:07:36.281Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:07:42.825Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:07:42.834Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:07:44.779Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:07:44.783Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:10:40.894Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:10:40.899Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:10:40.900Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:21:13.306Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:21:13.308Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:21:32.706Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:21:32.706Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:21:32.707Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:21:32.707Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:21:32.708Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:21:47.513Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:21:47.518Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:21:49.305Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:21:49.308Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:21:51.772Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:21:51.778Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:21:52.366Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:21:55.474Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:21:55.476Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:23:15.706Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:23:15.706Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:23:15.707Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:23:15.707Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:23:15.708Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:23:30.579Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:23:30.585Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:23:32.260Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:23:32.268Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:23:38.819Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:23:38.821Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:23:40.091Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:23:41.739Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:23:41.743Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:23:43.752Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:23:43.755Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:31:06.773Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:31:06.776Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:31:25.447Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:31:25.447Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:31:25.448Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:31:25.448Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:31:25.448Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:31:27.029Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:31:27.032Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:31:29.026Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:31:29.031Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:31:34.278Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:31:34.280Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:31:34.816Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:32:30.792Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:32:48.271Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:32:48.272Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:32:48.272Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:32:48.273Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:32:48.273Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:32:52.918Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:32:52.920Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:32:54.941Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:32:54.943Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:32:59.677Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:32:59.684Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:33:00.278Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:33:07.102Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:33:07.105Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:33:11.318Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:33:11.323Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:33:17.310Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:33:17.313Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:33:19.385Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:33:19.388Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:33:32.039Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:33:32.048Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:33:34.074Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:33:34.076Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:33:36.359Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:33:36.362Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:35:24.766Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:36:17.863Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:36:17.975Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-19T15:36:17.979Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-20T04:54:13.293Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-20T04:54:13.412Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-20T04:54:13.417Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:00:25.178Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:00:25.179Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:00:25.179Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:00:25.183Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:00:25.185Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:00:26.250Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:00:26.254Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:01:43.351Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:01:43.360Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:01:43.373Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:02:15.114Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:02:15.122Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:02:42.945Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:02:42.952Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:08:02.649Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:08:18.335Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:08:18.336Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:08:18.336Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:08:18.337Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:08:18.337Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:08:19.215Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:08:19.218Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:08:21.944Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:08:21.958Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:08:23.972Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:09:11.815Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:09:11.816Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:09:11.817Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:09:11.817Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:09:11.817Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:09:15.671Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:09:15.683Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:09:17.652Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:09:17.655Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:09:19.684Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:09:19.914Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:09:19.919Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:09:22.977Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:09:22.978Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:09:22.979Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:09:22.987Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:09:22.988Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:11:41.851Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:11:41.873Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:11:41.875Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:11:41.880Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:11:41.881Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:11:42.535Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:11:42.539Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:11:45.031Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:11:45.034Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:11:46.859Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:11:48.101Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:11:48.141Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:11:48.142Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:11:48.154Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:11:48.160Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:13:41.102Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:13:41.113Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:15:00.220Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:15:00.225Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:19:00.771Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:19:00.772Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:19:00.775Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:19:00.775Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:19:00.776Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:19:20.201Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:19:20.216Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:19:21.294Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:19:21.296Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:19:23.499Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:19:23.502Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:19:43.596Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:19:43.601Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:21:23.392Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:21:23.393Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:21:23.394Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:21:23.395Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:21:23.395Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:21:47.221Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:21:47.226Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:21:48.192Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:21:48.194Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:21:49.845Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:21:50.180Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:21:50.182Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:22:07.270Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:22:07.275Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:31:52.350Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:31:52.362Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:31:53.864Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:31:53.868Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:31:55.943Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:31:56.047Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:32:07.013Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:32:07.020Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:32:27.299Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:32:27.307Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:32:35.898Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:32:35.907Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:38:40.764Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:38:40.770Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:38:43.369Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:38:43.591Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:38:47.144Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:38:47.147Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:38:49.166Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:38:49.168Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:38:51.324Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:38:51.326Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:38:52.952Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:38:52.975Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:38:52.996Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:38:53.003Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:38:53.007Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:38:53.588Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:38:53.591Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:39:46.915Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:39:46.917Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:39:46.925Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:39:46.930Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:40:09.290Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:40:09.306Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:40:09.307Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:40:09.320Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:40:09.322Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:40:36.805Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:40:36.806Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:40:36.807Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:40:36.808Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:40:36.809Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:43:21.295Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:43:21.296Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:43:21.296Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:43:21.296Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:43:21.297Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:43:35.001Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:43:35.005Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:43:36.681Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:43:36.685Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:46:25.041Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:46:25.042Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:46:25.043Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:46:25.043Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:46:25.044Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:46:36.297Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:46:36.301Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:46:37.895Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:46:37.897Z"}
{"level":"info","message":"Getting child apps for child ID: 8 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:46:40.563Z"}
{"level":"info","message":"User 1 has direct relationship with child 8","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:46:40.575Z"}
{"level":"info","message":"Getting child apps for child ID: 13 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:46:46.582Z"}
{"level":"info","message":"User 1 has direct relationship with child 13","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:46:46.687Z"}
{"level":"info","message":"Getting child apps for child ID: 3 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:46:53.702Z"}
{"level":"info","message":"User 1 has direct relationship with child 3","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:46:53.706Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:46:55.419Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:46:55.423Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:50:29.040Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:50:29.041Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:50:29.041Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:50:29.042Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:50:29.043Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:50:32.118Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:50:32.122Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:50:34.127Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:50:34.131Z"}
{"level":"info","message":"Getting child apps for child ID: 3 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:50:37.809Z"}
{"level":"info","message":"User 1 has direct relationship with child 3","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:50:37.814Z"}
{"level":"info","message":"Getting child apps for child ID: 4 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:50:40.077Z"}
{"level":"info","message":"User 1 has direct relationship with child 4","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:50:40.083Z"}
{"level":"info","message":"Getting child apps for child ID: 4 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:50:49.426Z"}
{"level":"info","message":"User 1 has direct relationship with child 4","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:50:49.433Z"}
{"level":"info","message":"Getting child apps for child ID: 8 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:50:53.819Z"}
{"level":"info","message":"User 1 has direct relationship with child 8","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:50:53.821Z"}
{"level":"info","message":"Getting child apps for child ID: 13 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:50:56.066Z"}
{"level":"info","message":"User 1 has direct relationship with child 13","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:50:56.081Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:51:52.575Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:51:52.576Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:51:52.576Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:51:52.576Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:51:52.577Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:51:55.167Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:51:55.174Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:51:57.174Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:51:57.179Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:51:58.468Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:51:59.182Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:51:59.185Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:52:14.283Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:52:14.288Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:52:21.754Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:52:21.758Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:52:25.325Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:52:25.329Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:52:27.355Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:52:27.359Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:52:27.953Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:52:27.954Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:52:27.956Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:52:27.957Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:52:27.958Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:54:40.400Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:54:40.400Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:54:40.401Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:54:40.401Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:54:40.401Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:55:29.019Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:55:29.024Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:55:30.509Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:55:30.515Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:55:31.206Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:55:31.207Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:55:31.214Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:55:31.215Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:55:31.217Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:55:55.352Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:55:55.435Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:55:55.436Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:55:55.516Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:55:55.570Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:56:12.604Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:56:12.608Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:56:12.608Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:56:12.609Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:59:42.765Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:59:42.766Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:59:42.766Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T05:59:42.767Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:00:26.630Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:00:26.630Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:00:26.631Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:00:26.631Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:00:33.690Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:00:33.691Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:00:33.693Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:00:33.693Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:00:37.692Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:00:37.695Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:00:39.709Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:00:39.711Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:01:37.899Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:01:37.901Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:01:37.908Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:01:37.918Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:02:53.610Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:02:53.614Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:04:10.910Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:04:10.914Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:04:10.917Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:04:10.918Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:04:22.078Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:04:22.097Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:04:22.102Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:05:02.793Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:05:02.794Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:05:02.796Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:05:02.944Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:05:02.948Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:05:27.121Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:05:27.790Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:05:27.796Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:05:32.893Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:05:32.896Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:05:35.235Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:05:35.238Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:05:37.224Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:05:37.228Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:08:42.030Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:08:42.031Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:08:42.031Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:11:55.057Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:11:55.059Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:12:14.252Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:12:14.253Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:12:14.253Z"}
{"level":"info","message":"Getting child apps for child ID: 3 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:12:19.972Z"}
{"level":"info","message":"User 1 has direct relationship with child 3","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:12:19.977Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:12:21.936Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:12:21.940Z"}
{"level":"info","message":"Getting child apps for child ID: 13 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:12:24.308Z"}
{"level":"info","message":"User 1 has direct relationship with child 13","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:12:24.311Z"}
{"level":"info","message":"Getting child apps for child ID: 13 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:12:32.861Z"}
{"level":"info","message":"User 1 has direct relationship with child 13","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:12:32.869Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:12:35.499Z"}
{"level":"info","message":"Getting child apps for child ID: 13 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:12:42.521Z"}
{"level":"info","message":"User 1 has direct relationship with child 13","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:12:42.523Z"}
{"level":"info","message":"Getting child apps for child ID: 13 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:12:58.325Z"}
{"level":"info","message":"User 1 has direct relationship with child 13","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:12:58.332Z"}
{"level":"info","message":"Getting child apps for child ID: 13 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:13:00.127Z"}
{"level":"info","message":"User 1 has direct relationship with child 13","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:13:00.211Z"}
{"level":"info","message":"Getting child apps for child ID: 13 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:13:02.057Z"}
{"level":"info","message":"User 1 has direct relationship with child 13","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:13:02.059Z"}
{"level":"info","message":"Getting child apps for child ID: 13 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:13:02.079Z"}
{"level":"info","message":"User 1 has direct relationship with child 13","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:13:02.082Z"}
{"level":"info","message":"Getting child apps for child ID: 13 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:13:04.185Z"}
{"level":"info","message":"User 1 has direct relationship with child 13","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:13:04.188Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:13:06.133Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:13:06.137Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:13:06.139Z"}
{"level":"info","message":"Getting child apps for child ID: 13 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:13:59.110Z"}
{"level":"info","message":"User 1 has direct relationship with child 13","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:13:59.116Z"}
{"level":"info","message":"Getting child apps for child ID: 13 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:14:10.429Z"}
{"level":"info","message":"User 1 has direct relationship with child 13","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:14:10.433Z"}
{"level":"info","message":"Getting child apps for child ID: 13 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:14:12.204Z"}
{"level":"info","message":"User 1 has direct relationship with child 13","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:14:12.207Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:17:48.554Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:17:48.555Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:17:48.556Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:17:51.713Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:17:51.725Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:17:54.068Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:17:54.073Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:17:59.730Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:17:59.732Z"}
{"level":"info","message":"User connected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:18:01.473Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:18:07.207Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:18:07.215Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:18:12.018Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:18:12.023Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:18:14.048Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:18:14.052Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:18:17.993Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:18:17.999Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:18:18.000Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:27:49.312Z"}
{"level":"info","message":"User disconnected: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:27:49.315Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:28:10.400Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:28:10.400Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:28:10.401Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:28:18.231Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:28:18.232Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:28:18.233Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:29:06.345Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:29:06.352Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:29:07.812Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:29:07.820Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:34:13.403Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:34:13.404Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:34:13.404Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:34:16.609Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:34:16.611Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:34:18.545Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:34:18.547Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:34:32.119Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:34:32.124Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:34:33.968Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:34:33.974Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:34:40.326Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:34:40.328Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:34:42.344Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:34:42.348Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:34:44.348Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:34:44.352Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:34:44.397Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:34:44.403Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:41:02.898Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:41:02.898Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:41:02.898Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:41:11.000Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:41:11.004Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:41:13.021Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:41:13.026Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:54:28.812Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:54:28.813Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:54:28.813Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:54:31.784Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:54:31.784Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:54:31.785Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:54:34.651Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:54:34.665Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:54:36.668Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:54:36.671Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:54:41.747Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:54:41.753Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:54:43.745Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:54:43.748Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:58:27.405Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:58:27.406Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T06:58:27.406Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T07:01:38.384Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T07:01:38.385Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T07:01:38.385Z"}
{"level":"info","message":"Getting child apps for child ID: 2 by user ID: 1","service":"digital-wellbeing-api","timestamp":"2025-04-20T07:02:16.196Z"}
{"level":"info","message":"User 1 has direct relationship with child 2","service":"digital-wellbeing-api","timestamp":"2025-04-20T07:02:16.202Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-20T07:04:11.769Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-20T07:04:11.944Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-20T07:04:11.953Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-20T11:10:53.969Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-20T11:10:54.050Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-20T11:10:54.054Z"}
{"level":"info","message":"App usage data uploaded for user 13","service":"digital-wellbeing-api","timestamp":"2025-04-20T11:23:09.258Z"}
{"level":"info","message":"App usage data uploaded for user 13","service":"digital-wellbeing-api","timestamp":"2025-04-20T11:35:45.609Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T11:51:28.379Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T11:51:28.379Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T11:51:28.382Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T11:51:46.128Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T11:51:46.130Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T11:51:46.136Z"}
{"level":"info","message":"App usage data uploaded for user 13","service":"digital-wellbeing-api","timestamp":"2025-04-20T11:52:36.268Z"}
{"level":"info","message":"App usage data uploaded for user 13","service":"digital-wellbeing-api","timestamp":"2025-04-20T11:52:36.305Z"}
{"level":"info","message":"App usage data uploaded for user 13","service":"digital-wellbeing-api","timestamp":"2025-04-20T11:57:19.061Z"}
{"level":"info","message":"App usage data uploaded for user 13","service":"digital-wellbeing-api","timestamp":"2025-04-20T11:59:35.325Z"}
{"level":"info","message":"App usage data uploaded for user 13","service":"digital-wellbeing-api","timestamp":"2025-04-20T12:07:30.197Z"}
{"level":"info","message":"App usage data uploaded for user 13","service":"digital-wellbeing-api","timestamp":"2025-04-20T12:09:24.320Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-20T12:09:55.613Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-20T12:09:55.706Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-20T12:09:55.711Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-20T12:16:57.558Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-20T12:16:57.685Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-20T12:16:57.690Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-20T12:17:50.762Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-20T12:17:50.841Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-20T12:17:50.845Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-20T12:37:28.013Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-20T12:37:28.108Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-20T12:37:28.111Z"}
{"level":"info","message":"App usage data uploaded for user 13","service":"digital-wellbeing-api","timestamp":"2025-04-20T12:38:04.605Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T12:45:00.884Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T12:45:00.885Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T12:45:00.885Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T12:45:06.381Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T12:45:06.382Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T12:45:06.384Z"}
{"level":"info","message":"App usage data uploaded for user 13","service":"digital-wellbeing-api","timestamp":"2025-04-20T12:45:46.322Z"}
{"level":"info","message":"App usage data uploaded for user 13","service":"digital-wellbeing-api","timestamp":"2025-04-20T12:45:46.361Z"}
{"level":"info","message":"App usage data uploaded for user 13","service":"digital-wellbeing-api","timestamp":"2025-04-20T12:56:26.697Z"}
{"level":"info","message":"App usage data uploaded for user 13","service":"digital-wellbeing-api","timestamp":"2025-04-20T13:04:39.310Z"}
{"level":"info","message":"App usage data uploaded for user 13","service":"digital-wellbeing-api","timestamp":"2025-04-20T13:11:57.283Z"}
{"level":"info","message":"App usage data uploaded for user 13","service":"digital-wellbeing-api","timestamp":"2025-04-20T13:18:30.323Z"}
{"level":"info","message":"App usage data uploaded for user 13","service":"digital-wellbeing-api","timestamp":"2025-04-20T13:32:06.675Z"}
{"level":"info","message":"App usage data uploaded for user 13","service":"digital-wellbeing-api","timestamp":"2025-04-20T13:45:06.059Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-20T13:47:47.441Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-20T13:47:47.532Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-20T13:47:47.536Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-20T14:12:42.649Z"}
{"level":"info","message":"All models were synchronized successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-20T14:12:42.736Z"}
{"level":"info","message":"Server running on port 3000","service":"digital-wellbeing-api","timestamp":"2025-04-20T14:12:42.739Z"}
{"level":"info","message":"App usage data uploaded for user 13","service":"digital-wellbeing-api","timestamp":"2025-04-20T14:13:36.139Z"}
{"level":"info","message":"App usage data uploaded for user 13","service":"digital-wellbeing-api","timestamp":"2025-04-20T14:19:37.991Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T14:31:12.659Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T14:31:12.684Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T14:31:12.686Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T14:31:17.501Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T14:31:17.510Z"}
{"level":"info","message":"User 1 has access_level=true (true)","service":"digital-wellbeing-api","timestamp":"2025-04-20T14:31:17.512Z"}
{"level":"info","message":"App usage data uploaded for user 13","service":"digital-wellbeing-api","timestamp":"2025-04-20T14:31:54.550Z"}
{"level":"info","message":"App usage data uploaded for user 13","service":"digital-wellbeing-api","timestamp":"2025-04-20T14:31:54.561Z"}
{"level":"info","message":"App usage data uploaded for user 13","service":"digital-wellbeing-api","timestamp":"2025-04-20T14:32:15.191Z"}
{"level":"info","message":"App usage data uploaded for user 13","service":"digital-wellbeing-api","timestamp":"2025-04-20T14:32:15.213Z"}
{"level":"info","message":"App usage data uploaded for user 13","service":"digital-wellbeing-api","timestamp":"2025-04-20T14:32:30.870Z"}
{"level":"info","message":"Database connection has been established successfully.","service":"digital-wellbeing-api","timestamp":"2025-04-20T14:33:42.966Z"}

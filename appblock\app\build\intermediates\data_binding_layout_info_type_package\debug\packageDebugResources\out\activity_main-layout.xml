<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.appblock" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_main_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="311" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="13" startOffset="8" endLine="18" endOffset="66"/></Target><Target id="@+id/switch_app_blocking" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="69" startOffset="20" endLine="73" endOffset="66"/></Target><Target id="@+id/card_permissions" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="80" startOffset="12" endLine="116" endOffset="63"/></Target><Target id="@+id/tv_permission_status" view="TextView"><Expressions/><location startLine="105" startOffset="20" endLine="112" endOffset="49"/></Target><Target id="@+id/card_blocked_apps" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="119" startOffset="12" endLine="155" endOffset="63"/></Target><Target id="@+id/tv_blocked_apps_count" view="TextView"><Expressions/><location startLine="144" startOffset="20" endLine="151" endOffset="49"/></Target><Target id="@+id/card_blocked_websites" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="158" startOffset="12" endLine="192" endOffset="63"/></Target><Target id="@+id/tv_blocked_websites_count" view="TextView"><Expressions/><location startLine="181" startOffset="20" endLine="188" endOffset="49"/></Target><Target id="@+id/tv_vpn_status" view="TextView"><Expressions/><location startLine="221" startOffset="24" endLine="228" endOffset="53"/></Target><Target id="@+id/switch_vpn_blocking" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="232" startOffset="20" endLine="236" endOffset="66"/></Target><Target id="@+id/card_settings" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="243" startOffset="12" endLine="278" endOffset="63"/></Target><Target id="@+id/btn_setup_pin" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="291" startOffset="12" endLine="297" endOffset="46"/></Target><Target id="@+id/btn_device_admin" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="300" startOffset="12" endLine="305" endOffset="62"/></Target></Targets></Layout>